import React, { useMemo, useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { RTCView, MediaStream } from 'react-native-webrtc';

const DEFAULT_DIMENSIONS = {
  width: Dimensions.get('window').width,
  height: Dimensions.get('window').width * (16/9), // 16:9 aspect ratio
};

interface VideoDisplayProps {
  remoteStream: MediaStream | null;
  dimensions?: { width: number; height: number };
}

// Extract placeholder into a memoized component
const VideoPlaceholder = React.memo(({ text }: { text: string }) => (
  <View style={[styles.centerContent]}>
    <Text style={styles.infoText}>{text}</Text>
  </View>
));

// Extract RTCView into a memoized component
const VideoStream = React.memo(({ streamURL, style }: {
  streamURL: string;
  style: any;
}) => {
  if (!streamURL) {
    console.log('VideoStream: No streamURL provided');
    return null;
  }

  console.log(`VideoStream: Rendering RTCView with streamURL=${streamURL}`);

  // Force re-render after a short delay to ensure R<PERSON><PERSON>iew initializes properly
  const [key, setKey] = React.useState(0);
  React.useEffect(() => {
    // Force multiple re-renders to ensure RTCView initializes properly
    const timers: NodeJS.Timeout[] = [];
    for (let i = 1; i <= 5; i++) {
      const timer = setTimeout(() => {
        setKey(i);
        console.log(`VideoStream: Forced re-render of RTCView (attempt ${i})`);
      }, i * 300);
      timers.push(timer);
    }
    return () => timers.forEach(timer => clearTimeout(timer));
  }, [streamURL]);

  try {
    return (
      <RTCView
        key={`video-${key}`}
        streamURL={streamURL}
        style={style}
        objectFit="contain"
        zOrder={1}
        mirror={false}
      />
    );
  } catch (error) {
    console.error('RTCView error:', error);
    return (
      <View style={[style, styles.errorContainer]}>
        <Text style={styles.errorText}>Video Error</Text>
      </View>
    );
  }
});

export default function VideoDisplay({ remoteStream, dimensions = DEFAULT_DIMENSIONS }: VideoDisplayProps) {
  // Log video stream details for debugging
  useEffect(() => {
    if (remoteStream) {
      const videoTracks = remoteStream.getVideoTracks();
      console.log(`VideoDisplay: Remote stream has ${videoTracks.length} video tracks`);

      videoTracks.forEach(track => {
        console.log(`VideoDisplay: Video track details - id=${track.id}, enabled=${track.enabled}, readyState=${track.readyState}`);
        // Ensure video track is enabled
        if (!track.enabled) {
          track.enabled = true;
          console.log(`VideoDisplay: Enabled video track ${track.id}`);
        }
      });
    } else {
      console.log('VideoDisplay: No remote stream available');
    }
  }, [remoteStream]);

  // Memoize the stream URL computation
  const remoteStreamUrl = useMemo(() => {
    if (!remoteStream) {
      console.log('VideoDisplay: No remote stream available for URL creation');
      return null;
    }

    const videoTracks = remoteStream.getVideoTracks();
    if (videoTracks.length === 0) {
      console.log('VideoDisplay: Remote stream has no video tracks');
      return null;
    }

    try {
      const url = remoteStream.toURL();
      console.log(`VideoDisplay: Created stream URL: ${url}`);
      return url;
    } catch (error) {
      console.error('VideoDisplay: Stream URL error:', error);
      return null;
    }
  }, [remoteStream]);

  // Ensure dimensions are valid
  const validDimensions = useMemo(() => ({
    width: dimensions?.width || DEFAULT_DIMENSIONS.width,
    height: dimensions?.height || DEFAULT_DIMENSIONS.height
  }), [dimensions?.width, dimensions?.height]);

  // Memoize the video style
  const videoStyle = useMemo(() => ({
    position: 'absolute',
    top: 0,
    left: 0,
    width: validDimensions.width,
    height: validDimensions.height,
    backgroundColor: '#000'
  }), [validDimensions]);

  return (
    <View style={[styles.container, { width: validDimensions.width, height: validDimensions.height }]}>
      <View style={styles.videoPlaceholder}>
        {remoteStreamUrl ? (
          <VideoStream
            streamURL={remoteStreamUrl}
            style={videoStyle}
          />
        ) : (
          <VideoPlaceholder text="Waiting for remote video..." />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    backgroundColor: '#000',
  },
  videoPlaceholder: {
    flex: 1,
    backgroundColor: '#000',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  errorContainer: {
    backgroundColor: '#444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  }
});
