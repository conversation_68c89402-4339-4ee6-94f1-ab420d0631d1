import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { AlertCircle, CheckCircle, Info, X } from 'lucide-react-native';

export type AlertType = 'error' | 'success' | 'info' | 'warning';

interface AlertProps {
  type?: AlertType;
  title?: string;
  message: string;
  visible: boolean;
  onClose?: () => void;
  autoClose?: boolean;
  duration?: number;
  style?: ViewStyle;
}

export default function Alert({
  type = 'info',
  title,
  message,
  visible,
  onClose,
  autoClose = true,
  duration = 5000,
  style,
}: AlertProps) {
  const { colors } = useTheme();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(-20));

  useEffect(() => {
    if (visible) {
      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto close if enabled
      if (autoClose) {
        const timer = setTimeout(() => {
          handleClose();
        }, duration);

        return () => clearTimeout(timer);
      }
    } else {
      // Animate out
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -20,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const handleClose = () => {
    if (onClose) {
      // Animate out before calling onClose
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -20,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        onClose();
      });
    }
  };

  if (!visible) return null;

  // Get the appropriate icon and colors based on the alert type
  const getAlertStyles = () => {
    switch (type) {
      case 'error':
        return {
          backgroundColor: 'rgba(244, 67, 54, 0.1)',
          borderColor: colors.error,
          icon: <AlertCircle size={24} color={colors.error} />,
          titleColor: colors.error,
        };
      case 'success':
        return {
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          borderColor: colors.success,
          icon: <CheckCircle size={24} color={colors.success} />,
          titleColor: colors.success,
        };
      case 'warning':
        return {
          backgroundColor: 'rgba(255, 152, 0, 0.1)',
          borderColor: colors.warning || '#FF9800',
          icon: <AlertCircle size={24} color={colors.warning || '#FF9800'} />,
          titleColor: colors.warning || '#FF9800',
        };
      case 'info':
      default:
        return {
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          borderColor: colors.primary,
          icon: <Info size={24} color={colors.primary} />,
          titleColor: colors.primary,
        };
    }
  };

  const alertStyles = getAlertStyles();

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
          backgroundColor: alertStyles.backgroundColor,
          borderColor: alertStyles.borderColor,
        },
        style,
      ]}
    >
      <View style={styles.iconContainer}>{alertStyles.icon}</View>
      <View style={styles.contentContainer}>
        {title && (
          <Text style={[styles.title, { color: alertStyles.titleColor }]}>
            {title}
          </Text>
        )}
        <Text style={[styles.message, { color: colors.text }]}>{message}</Text>
      </View>
      <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
        <X size={20} color={colors.textSecondary} />
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  closeButton: {
    padding: 4,
  },
});
