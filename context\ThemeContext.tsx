import React, { createContext, useContext } from 'react';

// Define the theme colors
const COLORS = {
  primary: '#2563eb', // Blue
  primaryDark: '#1d4ed8',
  secondary: '#0ea5e9', // Sky blue
  accent: '#8b5cf6', // Purple
  success: '#10b981', // Green
  warning: '#f59e0b', // Amber
  error: '#ef4444', // Red
  background: '#ffffff',
  card: '#f9fafb',
  text: '#1f2937',
  textSecondary: '#6b7280',
  border: '#e5e7eb',
  notification: '#ef4444',
  statusSuccess: '#10b981',
  statusPending: '#3b82f6',
  statusError: '#ef4444',
};

// Define the theme interface
type ThemeContextType = {
  colors: typeof COLORS;
  spacing: (multiplier: number) => number;
};

// Create context
const ThemeContext = createContext<ThemeContextType>({
  colors: COLORS,
  spacing: (multiplier: number) => multiplier * 8,
});

// Provider component
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const spacing = (multiplier: number) => multiplier * 8;

  return (
    <ThemeContext.Provider value={{ colors: COLORS, spacing }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook for using the theme
export const useTheme = () => useContext(ThemeContext);

export default ThemeProvider;