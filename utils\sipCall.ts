import {
  mediaDevices,
  MediaStream,
} from 'react-native-webrtc';
import { Inviter, SessionState, UserAgent } from 'sip.js';

/**
 * Make a SIP call using WebRTC
 *
 * @param userAgent The SIP.js UserAgent
 * @param ext The extension, SIP URI, or username@domain to call
 *            If input doesn't start with "sip:", it will be added
 *            If input doesn't include a domain, "@urgent-route.com" will be appended
 * @param setStatus Function to update status
 * @param enableVideo Whether to enable video for this call
 * @param mediaStream The media stream to use for the call
 */
const sipCall = async (
  userAgent: UserAgent,
  ext: string,
  setStatus: (status: string) => void,
  enableVideo: boolean = false,
  mediaStream: MediaStream
) => {
  setStatus(`Preparing to call ${ext}`);

  try {
    // Enable video tracks if present
    const videoTracks = mediaStream.getVideoTracks();
    if (videoTracks.length > 0) {
      // Ensure video track is enabled
      videoTracks[0].enabled = true;
    }

    // Format the destination if needed
    if (!ext.startsWith('sip:')) {
      // Add sip: prefix
      ext = `sip:${ext}`;
    }

    // Check if the URI contains a domain (has an @ symbol)
    if (!ext.includes('@')) {
      // No domain specified, append the default domain
      ext = `${ext}@urgent-route.com`;
    }

    const targetURI = UserAgent.makeURI(ext);
    if (!targetURI) {
      throw new Error('Failed to create target URI');
    }

    // Configure SIP.js invite options with our media constraints
    const inviteOptions = {
      extraHeaders: ['X-App-Command: call'],
      sessionDescriptionHandlerOptions: {
        constraints: {
          audio: true,
          video: enableVideo,
        },
        // Add ICE servers configuration
        iceServers: [
          { urls: "stun:urgent-route.com:3478" },
          {
            urls: 'turn:urgent-route.com:3478',
            username: 'tkabe',
            credential: 'tkabe'
          }
        ],
        // Add tracks from our media stream
        tracks: mediaStream.getTracks()
      },
    };

    // Create the inviter with our options
    const inviter = new Inviter(userAgent, targetURI, inviteOptions);

    // Set up state change listener
    inviter.stateChange.addListener((state) => {
      if (state === SessionState.Establishing) {
        setStatus('Connecting...');
      } else if (state === SessionState.Established) {
        setStatus('Connected');
      } else if (state === SessionState.Terminated) {
        setStatus('Call ended');
        // Clean up resources
        mediaStream.getTracks().forEach(track => track.stop());
      }
    });

    // Start the call
    await inviter.invite();
    setStatus(`Calling ${ext}`);

    // We don't need to return the peerConnection as it can be accessed from the inviter:
    // inviter.sessionDescriptionHandler?.peerConnection

    // We still need to return the mediaStream because it's not directly accessible from the inviter
    // The mediaStream contains the local tracks that we need to manage (mute/unmute, etc.)
    return inviter;
  } catch (error) {
    console.error('SIP Call Error:', error);
    setStatus(`Error calling ${ext}`);

    // Provide more specific error messages based on the error
    if (error instanceof Error) {
      if (error.message.includes('Permission')) {
        throw new Error('Camera permission denied. Please enable camera access in your device settings.');
      } else if (error.message.includes('NotFoundError')) {
        throw new Error('No camera found. Please ensure your device has a camera and it is not in use by another application.');
      } else if (error.message.includes('NotReadableError')) {
        throw new Error('Camera is in use by another application. Please close other apps that might be using the camera.');
      }
    }
    throw error;
  }
};

export default sipCall;
