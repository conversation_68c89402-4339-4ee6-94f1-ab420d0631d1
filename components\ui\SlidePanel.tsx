import React, { useEffect } from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  StyleProp,
  ViewStyle,
  Platform,
} from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { X } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';

type SlidePanelPosition = 'left' | 'right' | 'top' | 'bottom';

interface SlidePanelProps {
  isVisible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  position?: SlidePanelPosition;
  width?: string | number;
  height?: string | number;
  style?: StyleProp<ViewStyle>;
}

export default function SlidePanel({
  isVisible,
  onClose,
  title,
  children,
  position = 'right',
  width: panelWidth = '80%',
  height: panelHeight = '100%',
  style,
}: SlidePanelProps) {
  const { colors } = useTheme();

  // Trigger haptic feedback when panel is shown
  useEffect(() => {
    if (isVisible && Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  }, [isVisible]);

  // Don't render if not visible
  if (!isVisible) {
    return null;
  }

  // Get position style based on position
  const getPositionStyle = (): any => {
    const positionStyles = {
      position: 'absolute' as 'absolute',
      zIndex: 100,
    };

    switch (position) {
      case 'left':
        return {
          ...positionStyles,
          left: 0,
          top: 0,
          bottom: 0,
          width: panelWidth,
        };
      case 'right':
        return {
          ...positionStyles,
          right: 0,
          top: 0,
          bottom: 0,
          width: panelWidth,
        };
      case 'top':
        return {
          ...positionStyles,
          top: 0,
          left: 0,
          right: 0,
          height: panelHeight,
        };
      case 'bottom':
        return {
          ...positionStyles,
          bottom: 0,
          left: 0,
          right: 0,
          height: panelHeight,
        };
      default:
        return {
          ...positionStyles,
          right: 0,
          top: 0,
          bottom: 0,
          width: panelWidth,
        };
    }
  };

  return (
    <View style={styles.container}>
      {/* Backdrop */}
      <View style={[styles.backdrop, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}>
        <TouchableOpacity
          style={styles.backdropTouchable}
          onPress={onClose}
          activeOpacity={1}
        />
      </View>

      {/* Panel */}
      <View
        style={[
          styles.panel,
          getPositionStyle(),
          {
            backgroundColor: colors.background,
            shadowColor: '#000',
            shadowOffset: { width: -2, height: 0 },
            shadowOpacity: 0.1,
            shadowRadius: 10,
            elevation: 10,
          },
          style,
        ]}
      >
        {title && (
          <View style={styles.header}>
            <Text style={[styles.title, { color: colors.text }]}>
              {title}
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <X size={24} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        )}

        <View style={styles.content}>
          {children}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  backdropTouchable: {
    flex: 1,
  },
  panel: {
    borderTopLeftRadius: 16,
    borderBottomLeftRadius: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
  },
  closeButton: {
    padding: 8,
    borderRadius: 8,
  },
  content: {
    flex: 1,
    padding: 24,
  },
});
