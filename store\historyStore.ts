import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage key for saving call history
const STORAGE_KEY = 'call_history';

export interface CallHistoryEntry {
  id: string;
  destination: string;
  displayName?: string;
  timestamp: Date;
  duration: number; // in seconds
  direction: 'incoming' | 'outgoing';
  status: 'answered' | 'missed' | 'rejected' | 'failed';
}

export interface HistoryState {
  callHistory: CallHistoryEntry[];
  
  // Actions
  addCallToHistory: (call: Omit<CallHistoryEntry, 'id'>) => void;
  clearHistory: () => void;
  deleteHistoryEntry: (id: string) => void;
  
  // Storage actions
  saveHistory: () => Promise<void>;
  loadHistory: () => Promise<void>;
}

export const useHistoryStore = create<HistoryState>((set, get) => ({
  callHistory: [],
  
  addCallToHistory: (call) => {
    const id = Date.now().toString();
    set((state) => {
      const newHistory = [
        { ...call, id },
        ...state.callHistory,
      ];
      
      // Save to AsyncStorage
      AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newHistory))
        .catch(error => console.error('Error saving call history:', error));
      
      return { callHistory: newHistory };
    });
  },
  
  clearHistory: () => {
    set({ callHistory: [] });
    AsyncStorage.removeItem(STORAGE_KEY)
      .catch(error => console.error('Error clearing call history:', error));
  },
  
  deleteHistoryEntry: (id) => {
    set((state) => {
      const newHistory = state.callHistory.filter(entry => entry.id !== id);
      
      // Save to AsyncStorage
      AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newHistory))
        .catch(error => console.error('Error saving call history:', error));
      
      return { callHistory: newHistory };
    });
  },
  
  saveHistory: async () => {
    try {
      const { callHistory } = get();
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(callHistory));
    } catch (error) {
      console.error('Error saving call history:', error);
    }
  },
  
  loadHistory: async () => {
    try {
      const savedHistory = await AsyncStorage.getItem(STORAGE_KEY);
      
      if (savedHistory) {
        // Parse dates from strings back to Date objects
        const parsedHistory = JSON.parse(savedHistory).map((entry: any) => ({
          ...entry,
          timestamp: new Date(entry.timestamp),
        }));
        
        set({ callHistory: parsedHistory });
      }
    } catch (error) {
      console.error('Error loading call history:', error);
    }
  },
}));
