/**
 * Basic types for SIP functionality
 */
import { Session } from 'sip.js';
import { MediaStream, MediaStreamTrack as RNMediaStreamTrack } from 'react-native-webrtc';

// Registration status
export type RegistrationStatus = 'not_registered' | 'registering' | 'registered' | 'failed';

// Call status
export type CallStatus = 'idle' | 'incoming' | 'calling' | 'active' | 'held' | 'ended';

// Transport protocol
export type Transport = 'UDP' | 'TCP' | 'TLS';

// Call direction
export type CallDirection = 'incoming' | 'outgoing';

// Registration form data
export interface RegistrationFormData {
  displayName: string;
  sipUri: string;
  password: string;
  outboundProxy: string;
  proxy?: string;
  enableVideo: boolean;
  port: number;
  transport: Transport;
  expires: number;
  autoRegister: boolean;
  connectionTimeout?: number; // Connection timeout in seconds
}

// Call information
export interface CallInfo {
  destination: string;
  startTime?: Date;
  direction: CallDirection;
}

// WebRTC Session information
// This interface contains all the WebRTC and SIP.js objects needed for a call
export interface CallSession {
  session: Session | null;
  localStream: MediaStream | null;
  remoteStream: MediaStream | null;
}

// Simple SIP URI parser
export function parseSipUri(uri: string): { username: string; domain: string } | null {
  const match = uri.match(/^(?:sip:)?([^@]+)@([^;>]+)/i);
  return match ? { username: match[1], domain: match[2] } : null;
}