import { RegistrationFormData } from '@/types/sip';
import type { SipState } from '@/store/sipStore';
import { useSipStore } from '@/store/sipStore';
import {
  mediaDevices,
  MediaStream,
  RTCPeerConnection,
  registerGlobals,
} from 'react-native-webrtc';
import { SessionState, UserAgent, Registerer, Session, UserAgentState, Invitation } from 'sip.js';
import { ISipService } from './mockSipService';
import sipCall from './sipCall';

// Initialize WebRTC globals
registerGlobals();

// Utility function to create a timeout promise
const createTimeoutPromise = <T>(ms: number, errorMessage: string): Promise<T> => {
  return new Promise<T>((_, reject) => {
    setTimeout(() => reject(new Error(errorMessage)), ms);
  });
};

/**
 * WebRTC SIP service implementation
 *
 * This is the main SIP service implementation that combines react-native-webrtc
 * with sip.js to provide SIP functionality in React Native environments.
 * It handles SIP registration, calling, and media management.
 */
export class WebRtcSipService implements ISipService {
  private callback: ((state: Partial<SipState>) => void) | null = null;
  private userAgent: UserAgent | null = null;
  private registerer: Registerer | null = null;
  private isRegistered = false;
  private callActive = false;
  private lastRegistrationData: RegistrationFormData | null = null;
  private reconnectionTimer: NodeJS.Timeout | null = null;
  private reconnectionAttempts = 0;
  private maxReconnectionAttempts = 10; // Maximum number of reconnection attempts
  private reconnectionDelay = 5000; // Initial delay between reconnection attempts (5 seconds)

  // Helper methods to access session, peer connection, and streams from Redux store
  private get session(): Session | null {
    return this.getStoreValue('session');
  }

  private set session(value: Session | null) {
    this.updateState({ session: value });
  }

  // Instead of storing the peerConnection in Redux, we'll get it from the session when needed
  private get peerConnection(): RTCPeerConnection | null {
    const session = this.session;
    if (!session) return null;

    // @ts-ignore - Accessing internal property
    return session.sessionDescriptionHandler?.peerConnection || null;
  }

  private get localStream(): MediaStream | null {
    return this.getStoreValue('localStream');
  }

  private set localStream(value: MediaStream | null) {
    this.updateState({ localStream: value });
  }

  private get remoteStream(): MediaStream | null {
    return this.getStoreValue('remoteStream');
  }

  private set remoteStream(value: MediaStream | null) {
    if (value) {
      this.updateState({
        remoteStream: value,
        remoteStreamAvailable: true
      });
    } else {
      this.updateState({
        remoteStream: null,
        remoteStreamAvailable: false
      });
    }
  }

  // Helper method to get values from the Redux store
  private getStoreValue<K extends keyof SipState>(key: K): SipState[K] {
    // Use the useSipStore directly to get the current state
    return useSipStore.getState()[key];
  }

  /**
   * Set callback for state updates
   * This callback is used to update the Redux store with SIP state changes
   */
  setStateUpdateCallback(callback: (state: Partial<SipState>) => void): void {
    this.callback = callback;
  }

  /**
   * Initialize the SIP service
   * Ensures any previous connections are properly closed before starting
   */
  async initialize(): Promise<boolean> {
    try {
      // Add a small delay to ensure any previous connections are properly closed
      await new Promise(resolve => setTimeout(resolve, 500));
      return true;
    } catch (error) {
      console.error('WebRTC SIP initialization error:', error);
      return false;
    }
  }

  /**
   * Clean up and destroy the SIP service
   * Terminates any active calls, unregisters from the SIP server,
   * and releases all media resources
   */
  async destroy(): Promise<void> {
    try {
      // Clear any reconnection timer
      this.clearReconnectionTimer();

      // End any active call
      if (this.session) {
        await this.session.bye().catch(e => console.error('Error ending call:', e));
        this.session = null;
      }

      // Unregister from SIP server
      if (this.registerer) {
        await this.registerer.unregister().catch(e => console.error('Error unregistering:', e));
        this.registerer = null;
      }

      // Stop the user agent
      if (this.userAgent) {
        await this.userAgent.stop().catch(e => console.error('Error stopping user agent:', e));
        this.userAgent = null;
      }

      // Clean up media resources
      this.cleanupMedia();

      // Reset state
      this.isRegistered = false;
      this.callActive = false;
      this.reconnectionAttempts = 0;
      this.lastRegistrationData = null;

      this.updateState({
        registrationStatus: 'not_registered',
        callStatus: 'idle',
        errorMessage: undefined
      });
    } catch (error) {
      console.error('Error destroying WebRTC SIP service:', error);
    }
  }

  /**
   * Check network connectivity by pinging the SIP server domain
   * @param server WebSocket URL of the SIP server
   * @returns Promise resolving to true if network is available, false otherwise
   */
  private async checkNetworkConnectivity(server: string): Promise<boolean> {
    try {
      // Extract domain from WebSocket URL
      const domain = this.extractDomainFromUrl(server);

      // Try to fetch from the server's domain with HTTPS
      const fetchPromise = fetch(`https://${domain}/`, { method: 'HEAD' });
      const timeoutPromise = createTimeoutPromise<Response>(5000, 'Network request timeout');

      try {
        // Race between fetch and timeout
        await Promise.race([fetchPromise, timeoutPromise]);
        return true;
      } catch (fetchError) {
        // Even if the fetch fails, we'll assume the network is available
        // The failure might be due to the server not supporting HTTPS or other reasons
        return true;
      }
    } catch (error) {
      // If there's an error in our check logic, we'll assume network is available
      return true;
    }
  }

  /**
   * Extract domain from a WebSocket URL
   * @param url WebSocket URL (e.g., wss://example.com:8443)
   * @returns Domain without protocol or port (e.g., example.com)
   */
  private extractDomainFromUrl(url: string): string {
    let domain = url;

    // Remove protocol prefix
    if (domain.startsWith('wss://')) {
      domain = domain.substring(6);
    } else if (domain.startsWith('ws://')) {
      domain = domain.substring(5);
    }

    // Remove port if present
    const colonIndex = domain.indexOf(':');
    if (colonIndex > 0) {
      domain = domain.substring(0, colonIndex);
    }

    return domain;
  }

  /**
   * Attempt to reconnect to the SIP server
   * Uses the last successful registration data to reconnect
   * Implements exponential backoff for retry attempts
   */
  private async attemptReconnect(): Promise<void> {
    // Clear any existing reconnection timer
    this.clearReconnectionTimer();

    // If we don't have registration data or have exceeded max attempts, don't try to reconnect
    if (!this.lastRegistrationData || this.reconnectionAttempts >= this.maxReconnectionAttempts) {
      console.log(`Not attempting reconnection: ${!this.lastRegistrationData ? 'No registration data' : 'Max attempts exceeded'}`);

      // Update UI to show registration failed
      this.updateState({
        registrationStatus: 'failed',
        errorMessage: 'Connection to SIP server lost. Please register again.'
      });

      return;
    }

    // Increment reconnection attempts
    this.reconnectionAttempts++;

    // Calculate delay with exponential backoff (5s, 10s, 20s, etc.)
    const delay = Math.min(this.reconnectionDelay * Math.pow(2, this.reconnectionAttempts - 1), 60000); // Max 60 seconds

    console.log(`Attempting reconnection ${this.reconnectionAttempts}/${this.maxReconnectionAttempts} in ${delay/1000} seconds`);

    // Update UI to show reconnection attempt
    this.updateState({
      registrationStatus: 'not_registered',
      errorMessage: `Connection lost. Reconnecting in ${Math.round(delay/1000)} seconds... (Attempt ${this.reconnectionAttempts}/${this.maxReconnectionAttempts})`
    });

    // Set timer for next reconnection attempt
    this.reconnectionTimer = setTimeout(async () => {
      try {
        console.log('Executing reconnection attempt');

        // Update UI to show reconnecting
        this.updateState({
          registrationStatus: 'registering',
          errorMessage: `Reconnecting to SIP server... (Attempt ${this.reconnectionAttempts}/${this.maxReconnectionAttempts})`
        });

        // Attempt to register with the stored data
        // We've already checked that lastRegistrationData is not null above
        const success = await this.register(this.lastRegistrationData!);

        if (success) {
          // Reset reconnection attempts on success
          this.reconnectionAttempts = 0;
          console.log('Reconnection successful');
        } else {
          // Schedule another reconnection attempt
          this.attemptReconnect();
        }
      } catch (error) {
        console.error('Error during reconnection attempt:', error);
        // Schedule another reconnection attempt
        this.attemptReconnect();
      }
    }, delay);
  }

  /**
   * Clear any existing reconnection timer
   */
  private clearReconnectionTimer(): void {
    if (this.reconnectionTimer) {
      clearTimeout(this.reconnectionTimer);
      this.reconnectionTimer = null;
    }
  }

  // Register with SIP server
  async register(data: RegistrationFormData): Promise<boolean> {
    try {
      // Store registration data for reconnection
      this.lastRegistrationData = { ...data };

      // Reset reconnection attempts
      this.reconnectionAttempts = 0;

      // Clear any existing reconnection timer
      this.clearReconnectionTimer();

      this.updateState({ registrationStatus: 'registering' });

      // Check network connectivity first
      const isConnected = await this.checkNetworkConnectivity(data.outboundProxy);
      if (!isConnected) {
        throw new Error('No network connectivity. Please check your internet connection and try again.');
      }

      // Clean up any existing connections first
      if (this.userAgent !== null || this.registerer !== null) {
        await this.cleanupExistingConnection();
      }

      // Normalize SIP URI - ensure it has the sip: prefix but only once
      let sipUri = data.sipUri;
      if (!sipUri.startsWith('sip:')) {
        sipUri = `sip:${sipUri}`;
      }

      // Create SIP.js user agent
      const uri = UserAgent.makeURI(sipUri);
      if (!uri) {
        throw new Error('Failed to create URI');
      }

      const server = data.outboundProxy;

      // Extract username without the sip: prefix for authentication
      let username = data.sipUri.split('@')[0];
      if (username.startsWith('sip:')) {
        username = username.substring(4); // Remove 'sip:' prefix
      }



      // Create user agent
      this.userAgent = new UserAgent({
        uri,
        authorizationUsername: username,
        authorizationPassword: data.password,
        displayName: data.displayName,
        transportOptions: {
          server,
          // Use connection timeout from settings or default to 3600 seconds (1 hour)
          connectionTimeout: data.connectionTimeout || 3600
        },
        // Add reconnection configuration for SIP.js internal reconnection
        // Note: We also implement our own reconnection logic for more control
        reconnectionAttempts: 3,
        reconnectionDelay: 4,
        logLevel: 'error'
      });

      // Add event listener for WebSocket errors
      if (this.userAgent.transport) {
        this.userAgent.transport.onConnect = () => {
          console.log('WebSocket connected successfully');
        };

        this.userAgent.transport.onDisconnect = (error?: Error) => {
          console.error('WebSocket disconnected:', error);

          if (this.isRegistered) {
            this.isRegistered = false;
            this.updateState({
              registrationStatus: 'not_registered',
              errorMessage: 'WebSocket connection lost. Attempting to reconnect...'
            });

            // Start the reconnection process
            this.attemptReconnect();
          }
        };
      }

      // Setup user agent
      this.userAgent.delegate = {
        onInvite: (invitation) => {
          // Handle incoming call
          this.updateState({
            callStatus: 'incoming',
            callInfo: {
              destination: invitation.remoteIdentity.uri.toString(),
              direction: 'incoming',
              startTime: new Date()
            }
          });

          // Setup session
          this.setupSession(invitation);
        }
      };

      // Create a promise that will resolve when the transport connects or reject when it fails
      const transportConnectPromise = new Promise<void>((resolve, reject) => {
        if (!this.userAgent || !this.userAgent.transport) {
          reject(new Error('User agent or transport not initialized'));
          return;
        }

        // Set up transport state change listener
        const transportStateListener = (state: string) => {

          if (state === 'Connected') {
            // Transport connected successfully
            resolve();
            // Remove the listener to avoid memory leaks
            this.userAgent?.transport?.stateChange.removeListener(transportStateListener);
          } else if (state === 'Disconnected' || state === 'Connecting') {
            // Keep waiting if connecting, or handle disconnection
            if (state === 'Disconnected') {
              console.log('Transport disconnected, will attempt to reconnect');

              // Update registration status if we were previously registered
              if (this.isRegistered) {
                this.isRegistered = false;
                this.updateState({
                  registrationStatus: 'not_registered',
                  errorMessage: 'Connection to SIP server lost. Attempting to reconnect...'
                });
              }
            }
          }
        };

        // Add the listener
        this.userAgent.transport.stateChange.addListener(transportStateListener);

        // Also add a permanent listener for transport state changes
        this.userAgent.transport.stateChange.addListener((state) => {
          if (state === 'Disconnected') {
            console.log('Transport disconnected permanently, initiating reconnection process');

            // Update registration status if we were previously registered
            if (this.isRegistered) {
              this.isRegistered = false;
              this.updateState({
                registrationStatus: 'not_registered',
                errorMessage: 'Connection to SIP server lost. Attempting to reconnect...'
              });

              // Start the reconnection process
              this.attemptReconnect();
            }
          }
        });
      });

      // Start user agent with a timeout and retry mechanism
      const startMaxRetries = 2; // Maximum number of retries
      let startRetryCount = 0;
      let startLastError: Error | null = null;

      while (startRetryCount <= startMaxRetries) {
        try {
          const startPromise = this.userAgent.start();
          const timeoutPromise = new Promise((_, reject) => {
            // Use connection timeout from settings or default to 30 seconds
            const timeoutMs = (data.connectionTimeout || 3600) * 1000;
            setTimeout(() => reject(new Error('User agent start timeout')), timeoutMs);
          });

          await Promise.race([startPromise, timeoutPromise]);
          console.log('User agent started successfully');

          // Wait for transport to connect with timeout
          const transportTimeoutPromise = new Promise<void>((_, reject) => {
            const timeoutMs = 10000; // 10 seconds timeout for transport connection
            setTimeout(() => reject(new Error('Transport connection timeout')), timeoutMs);
          });

          try {
            await Promise.race([transportConnectPromise, transportTimeoutPromise]);
          } catch (transportError) {
            console.error('Transport connection failed:', transportError);
            throw new Error('Failed to connect to SIP server. Please check your outbound proxy settings.');
          }

          // If successful, break out of the retry loop
          break;
        } catch (error) {
          startLastError = error as Error;
          startRetryCount++;

          if (startRetryCount <= startMaxRetries) {
            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, 2000));
          } else {
            console.error(`Failed to start user agent after ${startMaxRetries} retries`);
            throw startLastError;
          }
        }
      }

      // Create registerer with options
      this.registerer = new Registerer(this.userAgent, {
        // Add registerer options
        expires: data.expires || 120,
        refreshFrequency: 80 // Refresh at 80% of expiry time
      });

      // Create a promise that will resolve when registration is successful or reject when it fails
      const registrationPromise = new Promise<void>((resolve, reject) => {
        if (!this.registerer) {
          reject(new Error('Registerer not initialized'));
          return;
        }

        // Set up registerer state change listener
        const registererStateListener = (state: string) => {

          if (state === 'Registered') {
            // Registration successful
            resolve();
            // Remove the listener to avoid memory leaks
            this.registerer?.stateChange.removeListener(registererStateListener);
          } else if (state === 'Unregistered') {
            // Registration failed
            reject(new Error('Registration failed - server returned Unregistered status'));
            // Remove the listener to avoid memory leaks
            this.registerer?.stateChange.removeListener(registererStateListener);
          }
        };

        // Add the listener
        this.registerer.stateChange.addListener(registererStateListener);

        // Also add a permanent listener for registerer state changes
        this.registerer.stateChange.addListener((state) => {

          // Update our internal state based on registerer state
          if (state === 'Registered') {
            this.isRegistered = true;
            this.updateState({
              registrationStatus: 'registered',
              errorMessage: undefined
            });
          } else if (state === 'Unregistered') {
            this.isRegistered = false;
            this.updateState({
              registrationStatus: 'not_registered',
              errorMessage: 'Registration with SIP server lost'
            });
          }
        });
      });

      // Register with a timeout and retry mechanism
      const regMaxRetries = 2; // Maximum number of retries
      let regRetryCount = 0;
      let regLastError: Error | null = null;

      while (regRetryCount <= regMaxRetries) {
        try {
          const registerPromise = this.registerer.register();
          const registerTimeoutPromise = new Promise((_, reject) => {
            // Use a shorter timeout for registration
            const timeoutMs = 10000; // 10 seconds
            setTimeout(() => reject(new Error('Registration timeout')), timeoutMs);
          });

          // Start the registration process
          await Promise.race([registerPromise, registerTimeoutPromise]);

          // Wait for the registration to complete successfully
          const registrationTimeoutPromise = new Promise<void>((_, reject) => {
            const timeoutMs = 10000; // 10 seconds timeout for registration
            setTimeout(() => reject(new Error('Registration state change timeout')), timeoutMs);
          });

          try {
            await Promise.race([registrationPromise, registrationTimeoutPromise]);

          } catch (registrationError) {
            console.error('Registration state change failed:', registrationError);
            throw new Error('Failed to register with SIP server. Server returned an error.');
          }

          // If successful, break out of the retry loop
          break;
        } catch (error) {
          regLastError = error as Error;
          regRetryCount++;

          if (regRetryCount <= regMaxRetries) {

            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, 2000));
          } else {
            console.error(`Failed to register after ${regMaxRetries} retries`);
            throw regLastError;
          }
        }
      }

      // Update state
      this.isRegistered = true;
      this.updateState({
        registrationStatus: 'registered',
        sipUri: data.sipUri,
        errorMessage: undefined
      });

      return true;
    } catch (err) {
      const error = err as Error;
      console.error('Registration error:', error);

      // Provide more specific error messages based on the error
      let errorMessage = `Registration failed: ${error.message || 'Unknown error'}`;

      // Check for common connection errors
      if (error.message?.includes('timeout')) {
        errorMessage = 'Connection timed out. Please check your server address and try again, or increase the connection timeout in advanced settings.';
      } else if (error.message?.includes('Network request')) {
        errorMessage = 'Network connection issue. Please check your internet connection and try again.';
      } else if (error.message?.includes('Failed to fetch') || error.message?.includes('Network request failed')) {
        errorMessage = 'Failed to connect to the server. Please verify the server address is correct.';
      } else if (error.message?.includes('401') || error.message?.includes('403') || error.message?.includes('auth')) {
        errorMessage = 'Authentication failed. Please check your SIP URI and password.';
      } else if (error.message?.includes('WebSocket') || error.message?.includes('transport')) {
        errorMessage = 'WebSocket connection failed. Please check your outbound proxy settings and ensure the server is reachable.';
      } else if (error.message?.includes('503')) {
        errorMessage = 'SIP server returned Service Unavailable (503). The server may be down or unreachable.';
      }

      this.updateState({
        registrationStatus: 'failed',
        errorMessage: errorMessage
      });

      // Clean up after failure
      this.cleanupExistingConnection();

      return false;
    }
  }

  /**
   * Clean up existing SIP connections before creating new ones
   * Safely unregisters and stops any active SIP components
   */
  private async cleanupExistingConnection(): Promise<void> {
    try {
      let cleanupPerformed = false;

      // Unregister from SIP server if registered
      if (this.registerer) {
        try {
          await this.registerer.unregister().catch(() => {/* Ignore errors */});
          cleanupPerformed = true;
        } finally {
          this.registerer = null;
        }
      }

      // Stop the user agent if it's running
      if (this.userAgent && this.userAgent.state === UserAgentState.Started) {
        try {
          await this.userAgent.stop().catch(() => {/* Ignore errors */});
          cleanupPerformed = true;
        } finally {
          this.userAgent = null;
        }
      } else if (this.userAgent) {
        this.userAgent = null;
      }

      // Add a small delay to ensure connections are properly closed
      if (cleanupPerformed) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Error cleaning up existing connection:', error);
    }
  }

  // Unregister from SIP server
  async unregister(): Promise<boolean> {
    try {
      // Clear any reconnection timer
      this.clearReconnectionTimer();

      // Reset reconnection state
      this.reconnectionAttempts = 0;

      this.updateState({ registrationStatus: 'not_registered' });

      if (this.registerer) {
        try {
          await this.registerer.unregister();
        } catch (e) {
          console.error('Error during unregister:', e);
          // Even if unregister fails, we'll still consider it successful from the UI perspective
          // as we're going to clean up the connection anyway
        }
      }

      // Clean up the connection
      await this.cleanupExistingConnection();

      this.isRegistered = false;
      this.updateState({
        registrationStatus: 'not_registered',
        errorMessage: undefined
      });
      return true;
    } catch (error) {
      console.error('Unregister error:', error);

      // Clear any reconnection timer
      this.clearReconnectionTimer();

      // Even if there's an error, we'll still update the UI state
      this.isRegistered = false;
      this.updateState({
        registrationStatus: 'not_registered',
        errorMessage: undefined
      });

      return false;
    }
  }

  // Make a call
  async makeCall(destination: string, enableVideo: boolean): Promise<void> {
    if (!this.isRegistered || !this.userAgent) {
      throw new Error('Cannot make call: Not registered with SIP server');
    }

    try {
      // Update state to calling
      this.updateState({
        callStatus: 'calling',
        callInfo: {
          destination,
          direction: 'outgoing',
          startTime: new Date()
        }
      });

      // Initialize media stream first
      const mediaStream = await this.initializeMediaStream(enableVideo);

      // Store local stream immediately after creation
      this.localStream = mediaStream;

      // Get current mute states
      const { isMicrophoneMuted, isVideoMuted } = await this.getCurrentMuteState();

      // Apply mute states to tracks
      mediaStream.getAudioTracks().forEach(track => {
        track.enabled = !isMicrophoneMuted;
        console.log(`Initial audio track ${track.id} enabled: ${track.enabled}`);
      });

      mediaStream.getVideoTracks().forEach(track => {
        track.enabled = !isVideoMuted;
        console.log(`Initial video track ${track.id} enabled: ${track.enabled}`);
      });

      // Make the call using sipCall
      const inviter = await sipCall(
        this.userAgent,
        destination,
        () => {}, // setStatus callback
        enableVideo,
        mediaStream // Pass the already created mediaStream
      );

      // Store the session
      this.session = inviter;

      // Setup peer connection track handling
      if (this.peerConnection) {
        // @ts-ignore - ontrack exists in react-native-webrtc but TypeScript doesn't recognize it
        this.peerConnection.ontrack = this.handleTrackEvent.bind(this);
      }

      // Setup session state listeners
      this.setupSession(inviter);

    } catch (error) {
      console.error('Call error:', error);
      this.cleanupMedia();
      throw error;
    }
  }

  // Hang up a call
  async hangupCall(): Promise<void> {
    try {
      if (this.session) {
        await this.session.bye();
        this.session = null;
      }

      this.cleanupMedia();
      this.callActive = false;
      this.updateState({
        callStatus: 'idle',
        callInfo: undefined
      });
    } catch (error) {
      console.error('Hangup error:', error);
    }
  }

  // Answer an incoming call
  async answerCall(): Promise<void> {
    console.log('WebRTC Service: answerCall called');

    if (!this.session) {
      console.error('WebRTC Service: No session available for answering call');
      throw new Error('Cannot answer call: No incoming call available');
    }

    console.log(`WebRTC Service: Session state: ${this.session.state}`);
    console.log(`WebRTC Service: Session type: ${this.session.constructor.name}`);

    // Check if the session is in the correct state to be accepted
    if (this.session.state !== SessionState.Initial) {
      console.error(`WebRTC Service: Cannot accept call - session state is ${this.session.state}, expected ${SessionState.Initial}`);
      throw new Error(`Cannot accept call: Session is in ${this.session.state} state, expected Initial state`);
    }

    // Prevent multiple simultaneous accept attempts
    if (this.callActive) {
      console.log('WebRTC Service: Call is already being processed, ignoring duplicate answerCall');
      return;
    }

    try {
      // Get current form data for video settings
      let enableVideo = this.getStoreValue('formData').enableVideo;

      // Check if incoming SDP contains video
      // @ts-ignore - request property exists in Invitation but TypeScript doesn't recognize it
      const sdpBody = this.session.request?.body || '';
      const hasIncomingVideo = sdpBody.includes('m=video');

      console.log(`INCOMING CALL SDP ANALYSIS:
        Has video section: ${hasIncomingVideo}
        Local video enabled: ${enableVideo}
        SDP excerpt: ${sdpBody.substring(0, 200)}...`);

      // Always enable video for incoming calls with video, regardless of local settings
      if (hasIncomingVideo) {
        console.log('INCOMING VIDEO DETECTED IN SDP: Will enable video for answer');
        enableVideo = true;

        // Force remoteVideoAvailable to true since we know video is coming
        this.updateState({
          remoteVideoAvailable: true
        });
      }

      // Accept the call first without media to send SIP response quickly
      console.log('WebRTC Service: Accepting call first, then initializing media');

      try {
        const invitation = this.session as Invitation;

        // Simple accept without media constraints to ensure SIP response is sent
        await invitation.accept();
        console.log('WebRTC Service: Call accepted successfully - SIP response sent');

        // Update state to active immediately
        this.callActive = true;
        this.updateState({
          callStatus: 'active',
          errorMessage: undefined
        });

      } catch (acceptError) {
        console.error('WebRTC Service: Failed to accept call:', acceptError);
        throw new Error(`Failed to accept call: ${acceptError instanceof Error ? acceptError.message : String(acceptError)}`);
      }

      // Now initialize media stream with timeout
      console.log('WebRTC Service: Initializing media stream after call acceptance');
      let mediaStream: MediaStream | null = null;

      try {
        // Add timeout to media stream initialization
        const mediaPromise = this.initializeMediaStream(enableVideo);
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Media stream initialization timeout')), 10000); // 10 second timeout
        });

        mediaStream = await Promise.race([mediaPromise, timeoutPromise]);
        console.log(`WebRTC Service: Media stream created with ${mediaStream.getTracks().length} tracks`);

        // Store local stream
        this.localStream = mediaStream;

      } catch (mediaError) {
        console.error('WebRTC Service: Failed to initialize media stream after call acceptance:', mediaError);
        // Don't throw here - the call is already accepted, just continue without media for now
        console.log('WebRTC Service: Continuing without media stream - call is already active');
      }

      // Apply mute states to tracks if we have a media stream
      if (mediaStream) {
        // Get current mute states
        const { isMicrophoneMuted, isVideoMuted } = await this.getCurrentMuteState();

        // Apply mute states to tracks
        mediaStream.getAudioTracks().forEach(track => {
          track.enabled = !isMicrophoneMuted;
          console.log(`Initial audio track ${track.id} enabled: ${track.enabled}`);
        });

        mediaStream.getVideoTracks().forEach(track => {
          track.enabled = !isVideoMuted;
          console.log(`Initial video track ${track.id} enabled: ${track.enabled}`);
        });
      } else {
        console.log('WebRTC Service: No media stream available for mute state application');
      }

      // Set up ontrack handler BEFORE accepting the call
      // This ensures we don't miss any early tracks
      const pc = this.peerConnection;
      if (pc) {
        console.log('Setting up ontrack handler BEFORE accepting the call');
        // @ts-ignore - ontrack exists in react-native-webrtc but TypeScript doesn't recognize it
        pc.ontrack = this.handleTrackEvent.bind(this);
      } else {
        console.log('WebRTC Service: No peer connection available before accept');
      }

      console.log(`Accepting incoming call with audio=${true}, video=${hasIncomingVideo || enableVideo}`);

      // Accept the call with our media constraints
      try {
        console.log('WebRTC Service: Calling session.accept()');

        // Cast session to Invitation for incoming calls
        const invitation = this.session as Invitation;

        // Check if this is an Invitation object (incoming call)
        if (typeof invitation.accept === 'function') {
          console.log('WebRTC Service: Found accept method, calling it now');

          try {
            // Try with constraints first
            await invitation.accept({
              sessionDescriptionHandlerOptions: {
                constraints: {
                  audio: true,
                  video: hasIncomingVideo || enableVideo
                }
              }
            });
            console.log('WebRTC Service: session.accept() with constraints completed successfully');
          } catch (constraintsError) {
            console.warn('WebRTC Service: Accept with constraints failed, trying simple accept:', constraintsError);

            try {
              // Fallback to simple accept
              await invitation.accept();
              console.log('WebRTC Service: Simple session.accept() completed successfully');
            } catch (simpleAcceptError) {
              console.error('WebRTC Service: Simple accept also failed:', simpleAcceptError);
              throw simpleAcceptError;
            }
          }
        } else {
          console.error('WebRTC Service: session.accept is not a function');
          console.error('WebRTC Service: Session type:', this.session.constructor.name);
          console.error('WebRTC Service: Available methods:', Object.getOwnPropertyNames(this.session));
          throw new Error('Session does not have an accept method');
        }
      } catch (acceptError) {
        console.error('WebRTC Service: Error during session.accept():', acceptError);
        console.error('WebRTC Service: Accept error details:', {
          name: (acceptError as Error).name,
          message: (acceptError as Error).message,
          stack: (acceptError as Error).stack
        });
        throw acceptError;
      }

      // Get the peer connection again after accepting the call
      // It might have been recreated during the accept process
      const pcAfterAccept = this.peerConnection;

      if (pcAfterAccept) {
        console.log('Setting up ontrack handler AFTER accepting the call');
        // @ts-ignore - ontrack exists in react-native-webrtc but TypeScript doesn't recognize it
        pcAfterAccept.ontrack = this.handleTrackEvent.bind(this);

        // Log the current state of the peer connection
        console.log(`Peer connection state: ${pcAfterAccept.connectionState || 'unknown'}`);
        console.log(`ICE connection state: ${pcAfterAccept.iceConnectionState || 'unknown'}`);
        console.log(`Signaling state: ${pcAfterAccept.signalingState || 'unknown'}`);

        // Check for existing remote tracks that might have been added before we set the ontrack handler
        const receivers = pcAfterAccept.getReceivers();
        console.log(`Found ${receivers.length} receivers in peer connection`);

        receivers.forEach((receiver: any) => {
          if (receiver.track) {
            console.log(`Found existing track in receiver: kind=${receiver.track.kind}, id=${receiver.track.id}`);
            // Manually process this track since we might have missed the ontrack event
            this.handleTrackEvent({ track: receiver.track });
          }
        });

        // Handle media tracks if we have a media stream
        if (mediaStream) {
          // Get current mute states for track handling
          const { isMicrophoneMuted, isVideoMuted } = await this.getCurrentMuteState();

          // Ensure audio tracks are properly added to senders
          const audioTracks = mediaStream.getAudioTracks();
          if (audioTracks.length > 0) {
            // Check if audio tracks are already in senders
            // Use 'any' type to avoid TypeScript errors with react-native-webrtc types
            const audioSenders = pcAfterAccept.getSenders().filter(
              (sender: any) => sender.track?.kind === 'audio'
            );

            // If no audio senders found, manually add the tracks
            if (audioSenders.length === 0) {
              console.log('No audio senders found, manually adding audio tracks');
              audioTracks.forEach(track => {
                pcAfterAccept.addTrack(track, mediaStream!);
              });
            } else {
              console.log(`Found ${audioSenders.length} audio senders`);
              // Ensure tracks are enabled
              audioSenders.forEach((sender: any) => {
                if (sender.track) {
                  sender.track.enabled = !isMicrophoneMuted;
                  console.log(`Ensuring audio sender ${sender.track.id} is enabled: ${!isMicrophoneMuted}`);
                }
              });
            }
          }

          // Handle video tracks if video is enabled
          if (enableVideo) {
            const videoTracks = mediaStream.getVideoTracks();
            if (videoTracks.length > 0) {
              // Check if video tracks are already in senders
              const videoSenders = pcAfterAccept.getSenders().filter(
                (sender: any) => sender.track?.kind === 'video'
              );

              // If no video senders found, manually add the tracks
              if (videoSenders.length === 0) {
                console.log('No video senders found, manually adding video tracks');
                videoTracks.forEach(track => {
                  pcAfterAccept.addTrack(track, mediaStream!);
                });
              } else {
                console.log(`Found ${videoSenders.length} video senders`);
                // Ensure tracks are enabled
                videoSenders.forEach((sender: any) => {
                  if (sender.track) {
                    sender.track.enabled = !isVideoMuted;
                    console.log(`Ensuring video sender ${sender.track.id} is enabled: ${!isVideoMuted}`);
                  }
                });
              }
            } else {
              console.log('No local video tracks available despite video being enabled');
            }
          }
        } else {
          console.log('WebRTC Service: No media stream available for track handling');
        }
      }

      // Update state
      this.callActive = true;
      this.updateState({
        callStatus: 'active',
        errorMessage: undefined
      });

    } catch (error) {
      console.error('Answer call error:', error);
      this.cleanupMedia();
      throw error;
    }
  }

  /**
   * Mute/unmute microphone
   * When muted, audio tracks are disabled which prevents audio data from being sent
   * @param muted Whether the microphone should be muted
   */
  async setMicrophoneMuted(muted: boolean): Promise<void> {
    try {
      const pc = this.peerConnection;
      if (!pc) {
        console.log('No peer connection available for audio mute/unmute');
        return;
      }

      const senders = pc.getSenders().filter(sender => sender.track?.kind === 'audio');
      console.log(`Setting ${senders.length} audio senders to ${muted ? 'muted' : 'unmuted'}`);

      for (const sender of senders) {
        if (sender.track) {
          sender.track.enabled = !muted;
          console.log(`Audio sender ${sender.track.id} ${muted ? 'muted' : 'unmuted'}`);
        }
      }

      // Update UI state
      this.updateState({ isMicrophoneMuted: muted });
    } catch (error) {
      console.error('Audio mute error:', error);
    }
  }

  /**
   * Mute/unmute video
   * When muted, video tracks are disabled which prevents video data from being sent
   * @param muted Whether the video should be muted
   */
  async setVideoMuted(muted: boolean): Promise<void> {
    try {
      const pc = this.peerConnection;
      if (!pc) {
        console.log('No peer connection available for video mute/unmute');
        return;
      }

      const senders = pc.getSenders().filter(sender => sender.track?.kind === 'video');
      console.log(`Setting ${senders.length} video senders to ${muted ? 'muted' : 'unmuted'}`);

      for (const sender of senders) {
        if (sender.track) {
          sender.track.enabled = !muted;
          console.log(`Video sender ${sender.track.id} ${muted ? 'muted' : 'unmuted'}`);
        }
      }

      // Update UI state
      this.updateState({ isVideoMuted: muted });
    } catch (error) {
      console.error('Video mute error:', error);
    }
  }

  /**
   * Switch between front and back cameras
   * Uses react-native-webrtc's camera switching functionality
   */
  async switchCamera(): Promise<void> {
    try {
      if (!this.localStream) {
        return;
      }

      const videoTracks = this.localStream.getVideoTracks();
      if (videoTracks.length === 0) {
        return;
      }

      const videoTrack = videoTracks[0] as any; // Cast to any to access non-standard properties

      // Try to switch camera using the available method
      // react-native-webrtc has a non-standard _switchCamera method
      if (typeof videoTrack._switchCamera === 'function') {
        videoTrack._switchCamera();
      }
    } catch (error) {
      console.error('Error switching camera:', error);
    }
  }

  /**
   * Update application state via the callback
   * This method is used to communicate state changes to the Redux store
   * @param state Partial state object to update
   */
  private updateState(state: Partial<SipState>): void {
    if (this.callback) {
      this.callback(state);
    }
  }

  /**
   * Setup session state change listeners
   * Handles call state transitions and updates UI accordingly
   * @param session The SIP.js Session object
   */
  private setupSession(session: Session): void {
    this.session = session;

    console.log(`Setting up session: state=${session.state}`);

    // Set up the ontrack handler for the peer connection
    // This ensures we catch any tracks that might be added after the initial setup
    const peerConnection = this.peerConnection;
    if (peerConnection) {
      console.log('Setting up ontrack handler during session setup');
      // @ts-ignore - Using ontrack property which exists in react-native-webrtc but TypeScript doesn't recognize
      peerConnection.ontrack = this.handleTrackEvent.bind(this);

      // Also set up ICE connection state change listener for debugging
      // @ts-ignore - oniceconnectionstatechange exists in react-native-webrtc but TypeScript doesn't recognize it
      peerConnection.oniceconnectionstatechange = () => {
        console.log(`ICE connection state changed: ${peerConnection.iceConnectionState}`);

        // If ICE connection is established, check for existing tracks
        if (peerConnection.iceConnectionState === 'connected' ||
            peerConnection.iceConnectionState === 'completed') {

          // Check for existing remote tracks that might have been added before we set the ontrack handler
          const receivers = peerConnection.getReceivers();
          console.log(`ICE connected: Found ${receivers.length} receivers in peer connection`);

          receivers.forEach((receiver: any) => {
            if (receiver.track) {
              console.log(`Found existing track in receiver after ICE connected: kind=${receiver.track.kind}, id=${receiver.track.id}`);
              // Manually process this track since we might have missed the ontrack event
              this.handleTrackEvent({ track: receiver.track });
            }
          });
        }
      };
    }

    // Check if the session already has an SDP answer (for incoming calls)
    // @ts-ignore - sessionDescriptionHandler exists in Session but TypeScript doesn't recognize it
    const sdh = session.sessionDescriptionHandler;
    if (sdh) {
      // @ts-ignore - peerConnection exists in sessionDescriptionHandler but TypeScript doesn't recognize it
      const pc = sdh.peerConnection;
      if (pc) {
        const receivers = pc.getReceivers();

        console.log(`Session already has peer connection with ${receivers.length} receivers`);

        // Process any existing tracks
        receivers.forEach((receiver: any) => {
          if (receiver.track) {
            console.log(`Found existing track in initial session: kind=${receiver.track.kind}, id=${receiver.track.id}`);
            this.handleTrackEvent({ track: receiver.track });
          }
        });
      }
    }

    session.stateChange.addListener((state) => {
      console.log(`Session state changed to: ${state}`);

      switch (state) {
        case SessionState.Establishing:
          this.updateState({ callStatus: 'calling' });
          break;
        case SessionState.Established:
          this.callActive = true;
          this.updateState({ callStatus: 'active' });

          // Check again for peer connection and set up ontrack handler
          // This is important because the peer connection might be created after the session is established
          const pc = this.peerConnection;
          if (pc) {
            console.log('Setting up ontrack handler after session established');
            // @ts-ignore - Using ontrack property which exists in react-native-webrtc but TypeScript doesn't recognize
            pc.ontrack = this.handleTrackEvent.bind(this);

            // Check for existing tracks again
            const receivers = pc.getReceivers();
            console.log(`Session established: Found ${receivers.length} receivers in peer connection`);

            receivers.forEach((receiver: any) => {
              if (receiver.track) {
                console.log(`Found existing track after session established: kind=${receiver.track.kind}, id=${receiver.track.id}`);
                this.handleTrackEvent({ track: receiver.track });
              }
            });

            // Schedule additional checks for tracks that might be added later
            setTimeout(() => {
              if (this.peerConnection) {
                const delayedReceivers = this.peerConnection.getReceivers();
                console.log(`Delayed check: Found ${delayedReceivers.length} receivers`);

                delayedReceivers.forEach((receiver: any) => {
                  if (receiver.track) {
                    console.log(`Found track in delayed check: kind=${receiver.track.kind}, id=${receiver.track.id}`);
                    this.handleTrackEvent({ track: receiver.track });
                  }
                });
              }
            }, 1000);
          }
          break;
        case SessionState.Terminated:
          this.callActive = false;
          this.updateState({
            callStatus: 'idle',
            callInfo: undefined
          });
          this.cleanupMedia();
          this.session = null;
          break;
      }
    });
  }

  /**
   * Handle remote audio stream
   * In React Native, audio playback is handled automatically by the WebRTC implementation
   * This method ensures the UI state is updated to reflect audio availability
   * and that audio tracks are properly enabled
   */
  private playRemoteAudio(): void {
    if (!this.remoteStream) {
      console.log('Cannot play remote audio: No remote stream available');
      return;
    }

    try {
      // Log audio tracks for debugging
      const audioTracks = this.remoteStream.getAudioTracks();
      console.log(`Remote stream has ${audioTracks.length} audio tracks`);

      // Ensure all audio tracks are enabled
      audioTracks.forEach(track => {
        // Make sure track is enabled
        if (!track.enabled) {
          track.enabled = true;
          console.log(`Enabled remote audio track: id=${track.id}`);
        }

        console.log(`Remote audio track: id=${track.id}, enabled=${track.enabled}, readyState=${track.readyState}`);

        // Add event listeners for track state changes
        // @ts-ignore - These event handlers exist in react-native-webrtc but TypeScript doesn't recognize them
        if (typeof track.addEventListener === 'function') {
          track.addEventListener('ended', () => {
            console.log(`Remote audio track ended: id=${track.id}`);
          });

          track.addEventListener('mute', () => {
            console.log(`Remote audio track muted: id=${track.id}`);
          });

          track.addEventListener('unmute', () => {
            console.log(`Remote audio track unmuted: id=${track.id}`);
            // Re-enable track if it gets unmuted
            if (!track.enabled) {
              track.enabled = true;
              console.log(`Re-enabled remote audio track after unmute: id=${track.id}`);
            }
          });
        }
      });

      // Update state to indicate remote stream is available
      this.updateState({
        remoteStreamAvailable: true,
        remoteStream: this.remoteStream // Ensure the store has the latest stream reference
      });

      // Force audio routing to speaker/earpiece as needed
      // This is handled automatically by react-native-webrtc based on the device's state
      // (e.g., if headphones are connected, audio will go there)

      console.log('Remote audio stream ready for playback');
    } catch (error) {
      console.error('Error handling remote audio:', error);
    }
  }

  /**
   * Handle ontrack event from the peer connection
   * This method is called when a new track is received from the remote peer
   * It adds the track to the remote stream and updates the UI state
   * @param event The track event from the peer connection
   */
  private handleTrackEvent(event: any): void {
    // Extract track from event, handling different event formats
    const track = event.track || (event.streams && event.streams[0]?.getTracks()[0]);

    if (!track) {
      console.warn('Track event received but no track found in event:', event);
      return;
    }

    try {
      // Log detailed track information
      console.log(`Remote track received: kind=${track.kind}, id=${track.id}, enabled=${track.enabled}, readyState=${track.readyState}`);

      // Initialize remote stream if needed
      if (!this.remoteStream) {
        this.remoteStream = new MediaStream();
        console.log('Created new remote stream');
      }

      // Safety check
      if (!this.remoteStream) {
        console.error('Failed to create remote stream');
        return;
      }

      // Check if we already have this track
      const existingTrack = this.remoteStream.getTracks().find(t => t.id === track.id);
      if (!existingTrack) {
        // Add the track to our remote stream
        this.remoteStream.addTrack(track);
        console.log(`Added ${track.kind} track to remote stream`);

        // Enable the track
        track.enabled = true;

        // Add event listeners for track ended
        // @ts-ignore - These event handlers exist in react-native-webrtc but TypeScript doesn't recognize them
        if (typeof track.addEventListener === 'function') {
          track.addEventListener('ended', () => {
            console.log(`Remote ${track.kind} track ended: id=${track.id}`);
          });

          track.addEventListener('mute', () => {
            console.log(`Remote ${track.kind} track muted: id=${track.id}`);
          });

          track.addEventListener('unmute', () => {
            console.log(`Remote ${track.kind} track unmuted: id=${track.id}`);
            // Re-enable track if it gets unmuted
            if (!track.enabled) {
              track.enabled = true;
              console.log(`Re-enabled ${track.kind} track after unmute: id=${track.id}`);
            }
          });
        }

        // Update state based on track type
        if (track.kind === 'video') {
          // For video tracks, update video availability
          this.updateState({
            remoteVideoAvailable: true,
            remoteStreamAvailable: true,
            remoteStream: this.remoteStream
          });
          console.log('Remote video track received and enabled');

          // Force a re-render of the video component by updating the stream reference
          setTimeout(() => {
            if (this.remoteStream) {
              const updatedStream = this.remoteStream.clone();
              this.remoteStream = updatedStream;
              this.updateState({
                remoteStream: updatedStream
              });
              console.log('Forced remote stream update to trigger UI refresh');
            }
          }, 500);

        } else if (track.kind === 'audio') {
          // For audio tracks, update stream availability and play audio
          this.updateState({
            remoteStreamAvailable: true,
            remoteStream: this.remoteStream
          });
          this.playRemoteAudio();
          console.log('Remote audio track enabled and playing');
        }
      } else {
        console.log(`Track ${track.id} already exists in remote stream`);

        // Even if track exists, ensure it's enabled
        existingTrack.enabled = true;

        // For video tracks, make sure remoteVideoAvailable is set
        if (track.kind === 'video') {
          this.updateState({
            remoteVideoAvailable: true
          });
        }
      }

      // Log all tracks in the remote stream after adding this one
      const audioTracks = this.remoteStream.getAudioTracks();
      const videoTracks = this.remoteStream.getVideoTracks();
      console.log(`Remote stream now has ${audioTracks.length} audio and ${videoTracks.length} video tracks`);

      // If we have both audio and video tracks, ensure the state is updated
      if (audioTracks.length > 0 && videoTracks.length > 0) {
        this.updateState({
          remoteStreamAvailable: true,
          remoteVideoAvailable: true,
          remoteStream: this.remoteStream
        });
      }

    } catch (error) {
      console.error('Error handling track event:', error);
    }
  }

  /**
   * Get the current mute state for audio and video
   * This is used to ensure consistent mute state when starting a new call
   * @returns Object containing isMicrophoneMuted and isVideoMuted states
   */
  private async getCurrentMuteState(): Promise<{ isMicrophoneMuted: boolean, isVideoMuted: boolean }> {
    return new Promise((resolve) => {
      if (this.callback) {
        let muteState = {
          isMicrophoneMuted: false,
          isVideoMuted: false
        };

        // Create a function to extract mute states from the current state
        const getMuteState = (state: SipState): void => {
          muteState = {
            isMicrophoneMuted: state.isMicrophoneMuted || false,
            isVideoMuted: state.isVideoMuted || false
          };
        };

        // Call the callback with our function to get the current state
        this.callback({
          get: getMuteState
        } as any);

        resolve(muteState);
      } else {
        // If no callback is available, return default values
        resolve({
          isMicrophoneMuted: false,
          isVideoMuted: false
        });
      }
    });
  }

  /**
   * Clean up all media resources
   * Stops all tracks, closes peer connection, and resets state
   */
  private cleanupMedia(): void {
    // Stop all local media tracks
    const localStream = this.localStream;
    if (localStream) {
      try {
        localStream.getTracks().forEach(track => {
          track.stop();
          track.enabled = false; // Ensure track is disabled before stopping
        });
      } catch (e) {
        console.error('Error cleaning up local tracks:', e);
      }
      this.localStream = null;
    }

    // Clean up remote stream
    const remoteStream = this.remoteStream;
    if (remoteStream) {
      try {
        remoteStream.getTracks().forEach(track => {
          track.stop();
          track.enabled = false; // Ensure track is disabled before stopping
        });
      } catch (e) {
        console.error('Error cleaning up remote tracks:', e);
      }
      this.remoteStream = null;
    }

    // Close peer connection if it exists
    const peerConnection = this.peerConnection;
    if (peerConnection) {
      try {
        // Remove all event listeners - using 'any' type since React Native WebRTC types are different
        const pc = peerConnection as any;
        if (pc.ontrack) pc.ontrack = null;
        if (pc.onicecandidate) pc.onicecandidate = null;
        if (pc.oniceconnectionstatechange) pc.oniceconnectionstatechange = null;

        // Remove all transceivers to stop media processing
        if (pc.getTransceivers) {
          pc.getTransceivers().forEach((transceiver: any) => {
            if (transceiver.stop) transceiver.stop();
          });
        }

        // Close the connection
        peerConnection.close();
      } catch (e) {
        console.error('Error closing peer connection:', e);
      }
    }

    // Reset all media-related state
    this.updateState({
      remoteVideoAvailable: false,
      remoteStreamAvailable: false,
      isMicrophoneMuted: false,
      isVideoMuted: false
    });

    // Force garbage collection by removing references
    if (global.gc) {
      try {
        global.gc();
      } catch (e) {
        // Ignore if GC is not available
      }
    }
  }

  /**
   * Initialize media stream with audio and optional video
   * @param enableVideo Whether to enable video in the media stream
   * @returns A promise resolving to the created MediaStream
   */
  private async initializeMediaStream(enableVideo: boolean): Promise<MediaStream> {
    try {
      // Get media stream with constraints already applied during getUserMedia
      const stream = await mediaDevices.getUserMedia({
        audio: true,
        video: enableVideo ? {
          facingMode: 'user',
          width: { ideal: 320, max: 640 },
          height: { ideal: 240, max: 480 },
          frameRate: { ideal: 15, max: 24 }
        } : false
      });

      // Ensure tracks are enabled by default
      stream.getTracks().forEach(track => {
        track.enabled = true;
      });

      return stream;
    } catch (error) {
      console.error('Error initializing media stream:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const webrtcSipService = new WebRtcSipService();
