import { ISipService, mockSipService } from './mockSipService';
import { webrtcSipService } from './webrtcSipService';

/**
 * Get the SIP service implementation
 * This returns the WebRTC SIP service implementation which combines
 * react-native-webrtc with sip.js for React Native environments
 */
// Keep track of initialization state
let isInitialized = false;
let initializationPromise: Promise<ISipService> | null = null;

export async function getSipService(): Promise<ISipService> {
  // If already initialized, return the service immediately
  if (isInitialized) {
    return webrtcSipService;
  }

  // If initialization is in progress, return the existing promise
  if (initializationPromise) {
    return initializationPromise;
  }

  // Start initialization
  initializationPromise = (async () => {
    try {


      // Add a small delay to ensure any previous connections are properly closed
      await new Promise(resolve => setTimeout(resolve, 500));

      const success = await webrtcSipService.initialize();

      if (!success) {
        throw new Error('WebRTC SIP service initialization failed');
      }

      isInitialized = true;
      return webrtcSipService;
    } catch (error) {
      console.error('Error initializing WebRTC SIP service, falling back to mock:', error);
      return mockSipService;
    } finally {
      // Clear the promise so we can retry initialization if needed
      initializationPromise = null;
    }
  })();

  return initializationPromise;
}

// Re-export the ISipService interface
export { ISipService };
