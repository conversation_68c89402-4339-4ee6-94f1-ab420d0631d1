import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated, Easing, Dimensions } from 'react-native';
import { User } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';

// Get screen dimensions for responsive sizing
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface AvatarPlaceholderProps {
  name: string;
}

export default function AvatarPlaceholder({ name }: AvatarPlaceholderProps) {
  // Simplified to just two core animations
  const pulseAnim = useRef(new Animated.Value(0)).current;
  const floatAnim = useRef(new Animated.Value(0)).current;
  const { colors } = useTheme();

  useEffect(() => {
    // Create animations with different timings for avatar and shadow
    const startAnimations = () => {
      // Avatar pulse animation - for scaling effects
      const avatarPulseAnimation = Animated.loop(
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 4000, // 4 seconds for avatar pulse
          useNativeDriver: true,
          easing: Easing.inOut(Easing.sin), // Sine wave pattern ensures smooth looping
        }),
        { iterations: -1 }
      );

      // Avatar float animation - for subtle movement effects
      const avatarFloatAnimation = Animated.loop(
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 5500, // 5.5 seconds for avatar float
          useNativeDriver: true,
          easing: Easing.inOut(Easing.sin), // Sine wave pattern ensures smooth looping
        }),
        { iterations: -1 }
      );

      // Start animations with a slight delay between them for more natural feel
      avatarPulseAnimation.start();
      setTimeout(() => avatarFloatAnimation.start(), 1500);

      // Clean up animation on unmount
      return () => {
        avatarPulseAnimation.stop();
        avatarFloatAnimation.stop();
      };
    };

    // Start the animations
    const cleanup = startAnimations();
    return cleanup;
  }, []);

  // Carefully designed interpolations to ensure perfect looping

  // Avatar pulse effect - controls scaling
  const scale = pulseAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.98, 1.05, 0.98], // Start and end values match for seamless looping
  });

  // Shadow scale - reversed from avatar (when avatar grows, shadow shrinks)
  const shadowScale = pulseAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1.02, 0.95, 1.02], // Reversed effect with matching start/end values
  });

  // Avatar float effect - controls vertical movement
  const translateY = floatAnim.interpolate({
    inputRange: [0, 0.25, 0.5, 0.75, 1],
    outputRange: [0, -1, -2, -1, 0], // More points for smoother animation with matching start/end
  });

  // Shadow float - reversed from avatar (when avatar goes up, shadow goes down)
  const shadowTranslateY = floatAnim.interpolate({
    inputRange: [0, 0.25, 0.5, 0.75, 1],
    outputRange: [0, 0.5, 1, 0.5, 0], // Reversed with matching start/end values
  });

  // Make size responsive based on screen dimensions - larger now
  const containerSize = Math.min(SCREEN_WIDTH, SCREEN_HEIGHT) * 0.45;
  const circleSize = containerSize * 0.9;
  const avatarSize = containerSize * 0.75;
  const iconSize = avatarSize * 0.5; // Size for the user icon

  return (
    <View style={[styles.avatarContainer, { width: containerSize, height: containerSize }]}>
      {/* Shadow with reversed animation */}
      <Animated.View
        style={[
          styles.avatarShadow,
          {
            width: circleSize,
            height: circleSize,
            borderRadius: circleSize / 2,
            transform: [
              { scale: shadowScale }, // Reversed pulse animation
              { translateY: shadowTranslateY } // Reversed float animation
            ],
          },
        ]}
      />

      {/* Main avatar with just two animations: scale and float */}
      <Animated.View
        style={{
          transform: [
            { scale }, // Pulse animation
            { translateY } // Float animation
          ]
        }}
      >
        <View
          style={[
            styles.avatarCircle,
            {
              backgroundColor: colors.primary,
              width: avatarSize,
              height: avatarSize,
              borderRadius: avatarSize / 2,
            }
          ]}
        >
          <User size={iconSize} color="white" strokeWidth={1.5} />
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  avatarContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginBottom: 16,
  },
  avatarShadow: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.5,
    shadowRadius: 30,
    elevation: 15,
  },
  avatarCircle: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.25)',
    overflow: 'hidden',
  },
});
