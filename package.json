{"name": "sip-call-tester", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "expo start --dev-client", "build:web": "expo export --platform web", "lint": "expo lint", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@config-plugins/react-native-webrtc": "^10.0.0", "@expo-google-fonts/inter": "^0.2.3", "@expo/config-plugins": "~9.0.0", "@expo/vector-icons": "^14.0.2", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "date-fns": "^4.1.0", "expo": "~52.0.46", "expo-av": "~15.0.2", "expo-blur": "~14.0.3", "expo-camera": "~16.0.18", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-router": "~4.0.21", "expo-splash-screen": "^0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "^0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "lucide-react-native": "^0.475.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "^0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.9.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-sound": "^0.11.2", "react-native-svg": "15.8.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.19.13", "react-native-webrtc": "^124.0.5", "react-native-webview": "13.12.5", "sip.js": "^0.21.2", "zustand": "^4.5.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "typescript": "^5.3.3"}}