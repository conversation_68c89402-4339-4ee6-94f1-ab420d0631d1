import React, { createContext, useContext, useState, ReactNode } from 'react';
import { View, StyleSheet } from 'react-native';
import Alert, { AlertType } from '@/components/ui/Alert';

interface Notification {
  id: string;
  type: AlertType;
  title?: string;
  message: string;
  autoClose?: boolean;
  duration?: number;
}

interface NotificationContextType {
  showNotification: (
    type: AlertType,
    message: string,
    title?: string,
    autoClose?: boolean,
    duration?: number
  ) => string;
  hideNotification: (id: string) => void;
  hideAllNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      'useNotification must be used within a NotificationProvider'
    );
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider = ({
  children,
}: NotificationProviderProps) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const showNotification = (
    type: AlertType,
    message: string,
    title?: string,
    autoClose = true,
    duration = 5000
  ): string => {
    const id = Date.now().toString();
    const newNotification = {
      id,
      type,
      title,
      message,
      autoClose,
      duration,
    };

    setNotifications((prev) => [...prev, newNotification]);
    return id;
  };

  const hideNotification = (id: string) => {
    setNotifications((prev) =>
      prev.filter((notification) => notification.id !== id)
    );
  };

  const hideAllNotifications = () => {
    setNotifications([]);
  };

  return (
    <NotificationContext.Provider
      value={{ showNotification, hideNotification, hideAllNotifications }}
    >
      {children}
      <View style={styles.notificationContainer}>
        {notifications.map((notification) => (
          <Alert
            key={notification.id}
            type={notification.type}
            title={notification.title}
            message={notification.message}
            visible={true}
            onClose={() => hideNotification(notification.id)}
            autoClose={notification.autoClose}
            duration={notification.duration}
          />
        ))}
      </View>
    </NotificationContext.Provider>
  );
};

const styles = StyleSheet.create({
  notificationContainer: {
    position: 'absolute',
    top: 60,
    left: 16,
    right: 16,
    zIndex: 1000,
  },
});
