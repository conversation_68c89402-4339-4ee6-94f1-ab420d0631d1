import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  StatusBar,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, History as HistoryIcon, Phone, Clock, PhoneIncoming, PhoneOutgoing, PhoneMissed, Trash2 } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';
import { useRouter } from 'expo-router';
import { useHistoryStore, CallHistoryEntry } from '@/store/historyStore';
import { useSipStore } from '@/store/sipStore';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import { formatDistanceToNow } from 'date-fns';

export default function HistoryScreen() {
  const { colors } = useTheme();
  const router = useRouter();
  const { callHistory, loadHistory, clearHistory, deleteHistoryEntry } = useHistoryStore();
  const { makeCall } = useSipStore();

  // Load call history when the screen mounts
  useEffect(() => {
    loadHistory();
  }, []);

  const handleBackPress = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const handleClearHistory = () => {
    Alert.alert(
      'Clear Call History',
      'Are you sure you want to clear all call history?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            clearHistory();
            if (Platform.OS !== 'web') {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            }
          },
        },
      ]
    );
  };

  const handleDeleteEntry = (id: string) => {
    deleteHistoryEntry(id);
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  };

  const handleCallPress = (destination: string) => {
    makeCall(destination);
    router.push('/');
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const renderCallIcon = (entry: CallHistoryEntry) => {
    if (entry.status === 'missed') {
      return <PhoneMissed size={20} color={colors.error} />;
    } else if (entry.direction === 'incoming') {
      return <PhoneIncoming size={20} color={colors.success} />;
    } else {
      return <PhoneOutgoing size={20} color={colors.primary} />;
    }
  };

  const renderItem = ({ item }: { item: CallHistoryEntry }) => (
    <View style={[styles.callItem, { borderBottomColor: 'rgba(0, 0, 0, 0.05)' }]}>
      <View style={styles.callIconContainer}>
        {renderCallIcon(item)}
      </View>
      
      <View style={styles.callDetails}>
        <Text style={[styles.callDestination, { color: colors.text }]}>
          {item.displayName || item.destination}
        </Text>
        
        <View style={styles.callMetaContainer}>
          <Clock size={14} color={colors.textSecondary} style={styles.metaIcon} />
          <Text style={[styles.callTime, { color: colors.textSecondary }]}>
            {formatDistanceToNow(item.timestamp, { addSuffix: true })}
          </Text>
          
          {item.duration > 0 && (
            <>
              <View style={[styles.metaDot, { backgroundColor: colors.textSecondary }]} />
              <Text style={[styles.callDuration, { color: colors.textSecondary }]}>
                {formatDuration(item.duration)}
              </Text>
            </>
          )}
        </View>
      </View>
      
      <View style={styles.callActions}>
        <TouchableOpacity
          style={[styles.callButton, { backgroundColor: colors.primary }]}
          onPress={() => handleCallPress(item.destination)}
        >
          <Phone size={16} color="white" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteEntry(item.id)}
        >
          <Trash2 size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const EmptyHistory = () => (
    <View style={styles.emptyContainer}>
      <View style={[styles.emptyIconContainer, { backgroundColor: 'rgba(37, 99, 235, 0.1)' }]}>
        <HistoryIcon size={32} color={colors.primary} />
      </View>
      <Text style={[styles.emptyTitle, { color: colors.text }]}>No Call History</Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Your call history will appear here
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <SafeAreaView style={[styles.safeArea, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: 'rgba(0, 0, 0, 0.05)' }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackPress}
          >
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>

          <View style={styles.titleContainer}>
            <View style={styles.titleWrapper}>
              <HistoryIcon size={24} color={colors.primary} style={styles.titleIcon} />
              <Text style={[styles.titleText, { color: colors.text }]}>Call History</Text>
            </View>
          </View>

          {/* Empty view for balanced layout */}
          <TouchableOpacity 
            style={styles.clearButton}
            onPress={handleClearHistory}
            disabled={callHistory.length === 0}
          >
            <Trash2 size={20} color={callHistory.length === 0 ? 'transparent' : colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Call History List */}
        <FlatList
          data={callHistory}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={[
            styles.listContent,
            callHistory.length === 0 && styles.emptyListContent,
          ]}
          ListEmptyComponent={EmptyHistory}
        />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    width: 40,
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleIcon: {
    marginRight: 8,
  },
  titleText: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
  },
  clearButton: {
    width: 40,
    padding: 8,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listContent: {
    padding: 16,
  },
  emptyListContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  callItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  callIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    marginRight: 12,
  },
  callDetails: {
    flex: 1,
  },
  callDestination: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginBottom: 4,
  },
  callMetaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaIcon: {
    marginRight: 4,
  },
  callTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  metaDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 6,
  },
  callDuration: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  callActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  callButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  deleteButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
});
