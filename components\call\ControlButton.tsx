import React, { useRef, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, Animated, Easing } from 'react-native';

interface ControlButtonProps {
  isActive: boolean;
  onPress: () => void;
  activeIcon: React.ReactNode;
  inactiveIcon: React.ReactNode;
  size?: number;
  activeColor?: string;
  inactiveColor?: string;
  isToggleButton?: boolean;
}

export default function ControlButton({
  isActive,
  onPress,
  activeIcon,
  inactiveIcon,
  size = 60,
  activeColor = '#FF3B30',
  inactiveColor = 'rgba(255, 255, 255, 0.2)',
  isToggleButton = false
}: ControlButtonProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const backgroundColorAnim = useRef(new Animated.Value(isActive ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(backgroundColorAnim, {
      toValue: isActive ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
      easing: Easing.out(Easing.ease),
    }).start();
  }, [isActive]);

  const handlePress = () => {
    // Button press animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
        easing: Easing.out(Easing.bounce),
      }),
    ]).start();

    onPress();
  };

  const backgroundColor = backgroundColorAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [inactiveColor, activeColor],
  });

  return (
    <Animated.View
      style={{
        transform: [{ scale: scaleAnim }],
      }}
    >
      <TouchableOpacity
        onPress={handlePress}
        activeOpacity={0.8}
      >
        <Animated.View
          style={[
            styles.controlButtonContainer,
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              backgroundColor,
              borderWidth: isToggleButton ? 1.5 : 0,
              borderColor: isToggleButton ? 'rgba(255, 255, 255, 0.3)' : 'transparent'
            }
          ]}
        >
          {isActive ? activeIcon : inactiveIcon}
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  controlButtonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    elevation: 8,
    // Add a subtle inner shadow effect
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.15)',
  },
});
