import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { RegistrationStatus } from '@/types/sip';

interface StatusIndicatorProps {
  status: RegistrationStatus;
  errorMessage?: string;
}

export default function StatusIndicator({ status, errorMessage }: StatusIndicatorProps) {
  const { colors } = useTheme();
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
    
    // Automatically hide after 5 seconds if error
    if (status === 'failed') {
      const timer = setTimeout(() => {
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [status, errorMessage, fadeAnim]);
  
  // Get status color based on registration state
  const getStatusColor = () => {
    switch (status) {
      case 'registered':
        return colors.statusSuccess;
      case 'registering':
        return colors.statusPending;
      case 'failed':
        return colors.statusError;
      default:
        return colors.textSecondary;
    }
  };
  
  // Get status text
  const getStatusText = () => {
    switch (status) {
      case 'registered':
        return 'Registered';
      case 'registering':
        return 'Registering...';
      case 'failed':
        return 'Registration Failed';
      default:
        return 'Not Registered';
    }
  };
  
  return (
    <Animated.View 
      style={[
        styles.container, 
        { backgroundColor: getStatusColor() },
        { opacity: fadeAnim }
      ]}
    >
      <Text style={styles.statusText}>{getStatusText()}</Text>
      {status === 'failed' && errorMessage && (
        <Text style={styles.errorText}>{errorMessage}</Text>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 8,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  statusText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    textAlign: 'center',
  },
  errorText: {
    color: 'white',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    fontFamily: 'Inter-Regular',
  },
});