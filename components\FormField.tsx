import React from 'react';
import { View, Text, TextInput, StyleSheet, Switch } from 'react-native';
import { useTheme } from '@/context/ThemeContext';

interface FormFieldProps {
  label: string;
  value: string | boolean;
  onChangeText?: (text: string) => void;
  onValueChange?: (value: boolean) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'number-pad' | 'email-address' | 'url';
  error?: string;
  type?: 'text' | 'switch';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  disabled?: boolean;
}

export default function FormField({
  label,
  value,
  onChangeText,
  onValueChange,
  placeholder,
  secureTextEntry,
  keyboardType = 'default',
  error,
  type = 'text',
  autoCapitalize = 'none',
  disabled = false,
}: FormFieldProps) {
  const { colors } = useTheme();

  return (
    <View style={styles.container}>
      <Text style={[styles.label, { color: colors.text }]}>{label}</Text>

      {type === 'text' ? (
        <TextInput
          style={[
            styles.input,
            {
              borderColor: error ? colors.error : colors.border,
              color: disabled ? colors.textSecondary : colors.text,
              backgroundColor: disabled ? colors.border + '20' : 'transparent',
            },
          ]}
          value={value as string}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={colors.textSecondary}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          editable={!disabled}
        />
      ) : (
        <View style={styles.switchContainer}>
          <Switch
            value={value as boolean}
            onValueChange={onValueChange}
            trackColor={{ false: colors.border, true: colors.primary }}
            thumbColor="white"
          />
        </View>
      )}

      {error && (
        <Text style={[styles.errorText, { color: colors.error }]}>
          {error}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    fontFamily: 'Inter-Medium',
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  switchContainer: {
    height: 48,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
    fontFamily: 'Inter-Regular',
  },
});