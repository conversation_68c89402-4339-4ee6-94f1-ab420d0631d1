import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  SafeAreaView,
  Text,
} from 'react-native';
import { useSipStore } from '@/store/sipStore';
import { useTheme } from '@/context/ThemeContext';
import VideoDisplay from './VideoDisplay';
import CallControls from './CallControls';

// Get screen dimensions for responsive sizing
const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function ActiveCall() {
  const { colors } = useTheme();
  const {
    hangUp,
    callStatus,
    toggleMute,
    toggleVideo,
    switchCamera,
    isMicrophoneMuted,
    isVideoMuted,
    formData,
    localStream,
    remoteStream,
    callInfo,
  } = useSipStore();

  const [callDuration, setCallDuration] = useState(0);

  // Calculate local video dimensions as a percentage of screen size
  const localVideoWidth = SCREEN_WIDTH * 0.25; // 25% of screen width
  const localVideoHeight = localVideoWidth * 1.33; // 4:3 aspect ratio

  // Call duration timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (callStatus === 'active') {
      interval = setInterval(() => {
        setCallDuration((prev) => prev + 1);
      }, 1000);
    } else {
      // Reset duration when call is not active
      setCallDuration(0);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [callStatus]);

  // Format duration as MM:SS
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get call display name
  const getCallDisplayName = callInfo?.destination || 'Unknown';

  // Get the appropriate text for the call state
  const getCallStateText = (): string => {
    if (callStatus === 'active') {
      return formatDuration(callDuration);
    } else if (callStatus === 'calling') {
      return 'Connecting...';
    } else {
      return 'Initializing call...';
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Main view container with better structure */}
      <View style={styles.mainContainer}>
        {/* Remote Video Area */}
        <View style={styles.remoteVideo}>
          {/* Show VideoDisplay when call is active, video is enabled, and we have streams */}
          {callStatus === 'active' && formData.enableVideo ? (            <VideoDisplay
              remoteStream={remoteStream}
              dimensions={{
                width: SCREEN_WIDTH,
                height: SCREEN_WIDTH * (16/9) // 16:9 aspect ratio
              }}
            />
          ) : (
            <View style={[styles.simpleCallDisplay, { backgroundColor: colors.card }]}>
              <Text style={[styles.callerName, { color: colors.text }]}>
                {getCallDisplayName}
              </Text>
              <Text style={[styles.callStateText, { color: colors.textSecondary }]}>
                {getCallStateText()}
              </Text>
            </View>
          )}
        </View>

        {/* Call Controls */}
        <CallControls
          isMicrophoneMuted={isMicrophoneMuted}
          isVideoMuted={isVideoMuted}
          toggleMute={toggleMute}
          toggleVideo={toggleVideo}
          hangUp={hangUp}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e5e5e5',
  },
  mainContainer: {
    flex: 1,
    position: 'relative',
  },
  remoteVideo: {
    flex: 1,
    backgroundColor: '#e5e5e5',
  },
  simpleCallDisplay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
  },
  callerName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  callStateText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    textAlign: 'center',
    marginTop: 10,
  }
});
