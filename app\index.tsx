import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Keyboard,
  Pressable,
  StatusBar,
  Animated,
  Platform,
  TouchableOpacity,
  Text,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Settings, PhoneCall, History, Phone, PhoneOff, Phone as PhoneIcon, Mic, MicOff, Video, VideoOff } from 'lucide-react-native';
import { useSipStore } from '@/store/sipStore';
import { useTheme } from '@/context/ThemeContext';
import * as Haptics from 'expo-haptics';
import { useRouter } from 'expo-router';
import Header from '@/components/ui/Header';
import InputField from '@/components/ui/InputField';
import Button from '@/components/ui/Button';
import { useAnimations } from '@/hooks/useAnimations';
import ActiveCall from '@/components/call/ActiveCall';
import IncomingCall from '@/components/call/IncomingCall';

export default function MainScreen() {
  const { colors } = useTheme();
  const router = useRouter();
  const animations = useAnimations();

  // State
  const [destination, setDestination] = useState('');
  const {
    registrationStatus,
    makeCall,
    callStatus,
    lastCalledDestination,
  } = useSipStore();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [callType, setCallType] = useState('Urgentnet Conference');

  // Animations
  const buttonScaleAnim = animations.createValue(1);
  const hangupButtonScaleAnim = animations.createValue(1);

  // Set destination from lastCalledDestination when component mounts
  useEffect(() => {
    if (lastCalledDestination) {
      // Extract the extension number from the SIP URI
      let extensionNumber = lastCalledDestination;

      // Remove domain part if present
      const atIndex = extensionNumber.indexOf('@');
      if (atIndex > 0) {
        extensionNumber = extensionNumber.substring(0, atIndex);
      }

      // Remove sip: prefix if present
      if (extensionNumber.startsWith('sip:')) {
        extensionNumber = extensionNumber.substring(4);
      }

      // Set the destination field
      setDestination(extensionNumber);

      // Determine the call type based on the domain
      if (lastCalledDestination.includes('@urgentnet.works')) {
        setCallType('Urgentnet Conference');
      } else if (lastCalledDestination.includes('@healthnet.works')) {
        setCallType('Healthnet Conference');
      } else if (lastCalledDestination.includes('@urgent-route.com')) {
        setCallType('Direct Call');
      }

      console.log(`Loaded last called destination: ${extensionNumber}`);
    }
  }, [lastCalledDestination]);

  // Check registration status and call status
  const isRegistered = registrationStatus === 'registered';
  const isCallActive = callStatus === 'active' || callStatus === 'calling';
  const isIncomingCall = callStatus === 'incoming';

  const handleCall = () => {
    // Button press animation
    animations.buttonPress(buttonScaleAnim).start();

    // If not registered, navigate to settings
    if (!isRegistered) {
      // Provide haptic feedback
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }

      // Navigate to settings
      router.push('/settings');
      return;
    }

    // If registered but no destination, return
    if (!destination.trim()) return;

    // Provide haptic feedback when making a call
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }

    // Make the call
    if (callType == "Urgentnet Conference") makeCall(destination + "@urgentnet.works");
    if (callType == "Healthnet Conference") makeCall(destination + "@healthnet.works");
    if (callType == "Direct Call") makeCall(destination + "@urgent-route.com");
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <StatusBar barStyle="dark-content" />
      <Pressable style={{ flex: 1 }} onPress={Keyboard.dismiss}>
        {isCallActive ? (
          // Show the ActiveCall component when in a call
          <ActiveCall />
        ) : isIncomingCall ? (
          // Show the IncomingCall component when there's an incoming call
          <IncomingCall />
        ) : (
          <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
            {/* App Header */}
            <Header
              title="SIP Caller"
              leftIcon={<PhoneCall size={24} color={colors.primary} />}
              rightIcon={
                <View style={styles.headerButtons}>
                  <TouchableOpacity onPress={() => router.push('/history')}>
                    <History size={22} color={colors.text} style={{ marginRight: 12 }} />
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => router.push('/settings')}>
                    <Settings size={22} color={colors.text} />
                  </TouchableOpacity>
                </View>
              }
            />

            {/* Main Content */}
            <View style={styles.content}>
              <View style={styles.mainContainer}>
                <InputField
                  value={destination}
                  onChangeText={setDestination}
                  placeholder={isRegistered ? "Enter extension number" : "Register first to make calls"}
                  icon={<Phone size={20} color={colors.textSecondary} />}
                  keyboardType="url"
                  clearable
                  disabled={!isRegistered}
                />

                <View style={styles.dropdownContainer}>
                  <TouchableOpacity
                    style={[styles.dropdown, { borderColor: colors.border }]}
                    onPress={() => setIsDropdownOpen(!isDropdownOpen)}
                  >
                    <Text style={[styles.dropdownText, { color: colors.text }]}>
                      {callType || 'Select Call Type'}
                    </Text>
                  </TouchableOpacity>

                  {isDropdownOpen && (
                    <View style={[styles.dropdownList, { borderColor: colors.border }]}>
                      {['Urgentnet Conference', 'Healthnet Conference', 'Direct Call'].map((option) => (
                        <TouchableOpacity
                          key={option}
                          style={styles.dropdownItem}
                          onPress={() => {
                            setCallType(option);
                            setIsDropdownOpen(false);
                          }}
                        >
                          <Text style={[styles.dropdownItemText, { color: colors.text }]}>
                            {option}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>

                <Animated.View style={{ transform: [{ scale: buttonScaleAnim }], marginTop: 16 }}>
                  <Button
                    title={isRegistered ? 'Call' : 'Go to Settings'}
                    onPress={handleCall}
                    size="lg"
                    style={styles.callButton}
                  />
                </Animated.View>
              </View>
            </View>
          </SafeAreaView>
        )}
      </Pressable>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 24,
    justifyContent: 'center',
  },
  mainContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },
  callButton: {
    height: 56,
  },
  callActiveContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  callStatusText: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
  },
  callControlsContainer: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  controlButton: {
    width: 52,
    height: 52,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 4,
  },
  hangupButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  dropdownContainer: {
    marginTop: 12,
    position: 'relative',
  },
  dropdown: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  dropdownText: {
    fontSize: 16,
  },
  dropdownList: {
    position: 'absolute',
    top: 52,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderRadius: 8,
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  dropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dropdownItemText: {
    fontSize: 16,
  },
});