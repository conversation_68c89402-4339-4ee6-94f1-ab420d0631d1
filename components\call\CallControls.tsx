import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { Mic, MicOff, PhoneOff, Video, VideoOff } from 'lucide-react-native';
import ControlButton from './ControlButton';

interface CallControlsProps {
  isMicrophoneMuted: boolean;
  isVideoMuted: boolean;
  toggleMute: () => void;
  toggleVideo: () => void;
  hangUp: () => void;
}

export default function CallControls({
  isMicrophoneMuted,
  isVideoMuted,
  toggleMute,
  toggleVideo,
  hangUp
}: CallControlsProps) {
  return (
    <View style={styles.controlsContainer}>
      {/* Mute But<PERSON> */}
      <ControlButton
        isActive={isMicrophoneMuted}
        onPress={toggleMute}
        activeIcon={<MicOff size={24} color="white" strokeWidth={2} />}
        inactiveIcon={<Mic size={24} color="white" strokeWidth={2} />}
        size={60}
        activeColor="#777777" // Gray when muted
        inactiveColor="#2196F3" // Blue when unmuted
        isToggleButton={true}
      />

      {/* Hangup Button */}
      <ControlButton
        isActive={true}
        onPress={hangUp}
        activeIcon={<PhoneOff size={32} color="white" strokeWidth={2} />}
        inactiveIcon={<PhoneOff size={32} color="white" strokeWidth={2} />}
        size={72}
        activeColor="#FF3B30"
        inactiveColor="#FF3B30"
      />

      {/* Video Button */}
      <ControlButton
        isActive={isVideoMuted}
        onPress={toggleVideo}
        activeIcon={<VideoOff size={24} color="white" strokeWidth={2} />}
        inactiveIcon={<Video size={24} color="white" strokeWidth={2} />}
        size={60}
        activeColor="#777777" // Gray when muted
        inactiveColor="#2196F3" // Blue when unmuted
        isToggleButton={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  controlsContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 40 : 30,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingHorizontal: 16,
    zIndex: 10,
  },
});
