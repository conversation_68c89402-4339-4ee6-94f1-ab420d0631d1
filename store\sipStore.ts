import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { RegistrationFormData, RegistrationStatus, CallStatus, CallInfo, Transport, CallSession } from '@/types/sip';
import { getSipService } from '@/utils/sipServiceFactory';
import { useHistoryStore } from './historyStore';
import { MediaStream, MediaStreamTrack as RNMediaStreamTrack } from 'react-native-webrtc';
import { Session } from 'sip.js';

// Storage keys for saving settings and last called destination
const STORAGE_KEY = 'sip_settings';
const LAST_DESTINATION_KEY = 'last_destination';

// Default form data with test values
const defaultFormData: RegistrationFormData = {
  displayName: 'Test User',
  sipUri: '<EMAIL>',
  password: 'password',
  outboundProxy: 'wss://example.com:4443',
  enableVideo: true, // Enable video by default
  port: 5060,
  transport: 'TCP' as Transport,
  expires: 600,
  autoRegister: false,
  connectionTimeout: 3600 // Default connection timeout in seconds
};

export interface SipState {
  registrationStatus: RegistrationStatus;
  callStatus: CallStatus;
  errorMessage?: string;
  callInfo?: CallInfo;
  isMicrophoneMuted: boolean;
  isVideoMuted: boolean;
  callStartTime?: Date;
  remoteVideoAvailable?: boolean;
  remoteStreamAvailable?: boolean;
  isRegistrationInProgress: boolean; // Track if registration is in progress
  lastCalledDestination?: string; // Store the last called destination

  // WebRTC and SIP.js session objects
  session: Session | null;
  localStream: MediaStream | null;
  remoteStream: MediaStream | null;

  // Form data for SIP registration
  formData: RegistrationFormData;

  // Actions
  updateFormData: (updates: Partial<RegistrationFormData>) => void;
  register: () => Promise<boolean>;
  unregister: () => Promise<boolean>;
  makeCall: (destination: string) => Promise<boolean>;
  answerCall: () => Promise<void>;
  hangUp: () => Promise<void>;
  toggleMute: () => Promise<void>;
  toggleVideo: () => Promise<void>;
  switchCamera: () => Promise<void>;

  // Media stream actions
  updateSession: (session: Session | null) => void;
  updateLocalStream: (localStream: MediaStream | null) => void;
  updateRemoteStream: (remoteStream: MediaStream | null) => void;
  addTrackToRemoteStream: (track: RNMediaStreamTrack) => void;

  // Storage actions
  saveSettings: () => Promise<void>;
  loadSettings: () => Promise<boolean>;

  // Computed properties
  hasOngoingCall: boolean;
  sipUri: string;
  displayName: string;
}

export const useSipStore = create<SipState>((set, get) => ({
  registrationStatus: 'not_registered',
  callStatus: 'idle',
  errorMessage: undefined,
  callInfo: undefined,
  isMicrophoneMuted: false,
  isVideoMuted: false,
  remoteVideoAvailable: false,
  remoteStreamAvailable: false,
  isRegistrationInProgress: false,
  lastCalledDestination: undefined,

  // WebRTC and SIP.js session objects
  session: null,
  peerConnection: null,
  localStream: null,
  remoteStream: null,

  formData: { ...defaultFormData },

  updateFormData: (updates) => {
    set((state) => {
      const newFormData = {
        ...state.formData,
        ...updates,
      };

      // Save settings to AsyncStorage
      AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newFormData))
        .catch(error => console.error('Error saving settings:', error));

      return { formData: newFormData };
    });
  },

  register: async () => {
    const { formData, isRegistrationInProgress } = get();

    // If registration is already in progress, don't start another one
    if (isRegistrationInProgress) {
      return false;
    }

    // Mark registration as in progress
    set({ isRegistrationInProgress: true });

    try {
      // Get the SIP service
      const sipService = await getSipService();

      // Call SIP service to register
      const result = await sipService.register(formData);

      // Mark registration as no longer in progress
      set({ isRegistrationInProgress: false });

      return result;
    } catch (error) {
      console.error('Registration error:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);

      set({
        registrationStatus: 'failed',
        errorMessage: errorMessage,
        isRegistrationInProgress: false // Make sure to reset the flag on error
      });
      return false;
    }
  },

  unregister: async () => {
    try {
      // Get the SIP service
      const sipService = await getSipService();

      // Call SIP service to unregister
      return await sipService.unregister();
    } catch (error) {
      console.error('Unregister error:', error);
      return false;
    }
  },

  makeCall: async (destination) => {
    const { formData } = get();
    try {
      // If video is enabled, ensure video is not muted when starting the call
      if (formData.enableVideo) {
        set({ isVideoMuted: false, isMicrophoneMuted: false });
      }

      // Save the destination as the last called destination
      set({ lastCalledDestination: destination });

      // Save to AsyncStorage for persistence
      try {
        await AsyncStorage.setItem(LAST_DESTINATION_KEY, destination);
        console.log(`Saved last called destination: ${destination}`);
      } catch (storageError) {
        console.error('Error saving last called destination:', storageError);
      }

      // Start call
      const sipService = await getSipService();
      await sipService.makeCall(destination, formData.enableVideo);

      // Record the call start in history
      const callStartTime = new Date();

      // Store call start time for duration calculation
      set(state => ({
        ...state,
        callStartTime
      }));

      return true;
    } catch (error) {
      console.error('Call error:', error);

      // Record failed call in history
      useHistoryStore.getState().addCallToHistory({
        destination,
        displayName: undefined,
        timestamp: new Date(),
        duration: 0,
        direction: 'outgoing',
        status: 'failed'
      });

      return false;
    }
  },

  answerCall: async () => {
    try {
      const { formData } = get();

      // If the call has video, ensure video settings are properly initialized
      if (formData.enableVideo) {
        set({
          isVideoMuted: false,
          isMicrophoneMuted: false,
          remoteVideoAvailable: true // Pre-enable video availability for incoming video calls
        });

        console.log('Store: Local and remote video enabled for incoming call');
      }

      const sipService = await getSipService();
      await sipService.answerCall();

      // Record the call start time for duration calculation
      set(state => ({
        ...state,
        callStartTime: new Date()
      }));

      // Add multiple checks for remote video tracks to handle race conditions
      const checkRemoteVideo = () => {
        const currentState = get();
        if (currentState.remoteStream) {
          const videoTracks = currentState.remoteStream.getVideoTracks();
          if (videoTracks.length > 0) {
            // Enable all video tracks
            videoTracks.forEach(track => {
              track.enabled = true;
              console.log(`Enabled remote video track: ${track.id}`);
            });

            if (!currentState.remoteVideoAvailable) {
              set({ remoteVideoAvailable: true });
              console.log('Detected and enabled remote video tracks');
            }
          }
        }
      };

      // Check multiple times to handle async track additions
      [0, 500, 1000, 2000].forEach(delay => {
        setTimeout(checkRemoteVideo, delay);
      });
    } catch (error) {
      console.error('Store: Error answering call:', error);

      // Update state to show error
      set({
        errorMessage: `Failed to answer call: ${error instanceof Error ? error.message : String(error)}`,
        callStatus: 'idle'
      });
    }
  },

  hangUp: async () => {
    try {
      const { callInfo, callStartTime } = get();
      const sipService = await getSipService();
      await sipService.hangupCall();

      // Record the call in history if we have call info
      if (callInfo) {
        const endTime = new Date();
        const startTime = callStartTime || new Date(endTime.getTime() - 1000); // Fallback if no start time
        const duration = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);

        useHistoryStore.getState().addCallToHistory({
          destination: callInfo.destination,
          displayName: undefined, // Could extract from destination if needed
          timestamp: startTime,
          duration,
          direction: callInfo.direction,
          status: 'answered' // Assuming call was answered if we're hanging up
        });

        // Clear call start time
        set(state => ({
          ...state,
          callStartTime: undefined
        }));
      }
    } catch (error) {
      console.error('Error hanging up call:', error);
    }
  },

  toggleMute: async () => {
    try {
      const { isMicrophoneMuted } = get();
      const sipService = await getSipService();
      await sipService.setMicrophoneMuted(!isMicrophoneMuted);
      set({ isMicrophoneMuted: !isMicrophoneMuted });
    } catch (error) {
      console.error('Error toggling mute:', error);
    }
  },

  toggleVideo: async () => {
    try {
      const { isVideoMuted } = get();
      const newMutedState = !isVideoMuted;



      // Update state first for immediate UI feedback
      set({ isVideoMuted: newMutedState });

      // Then update the service - this only affects local video
      const sipService = await getSipService();
      await sipService.setVideoMuted(newMutedState);

      // No need to update remoteVideoAvailable since we're only controlling local video
    } catch (error) {
      console.error('Error toggling video:', error);
      // Revert state on error
      set(state => ({ isVideoMuted: !state.isVideoMuted }));
    }
  },

  switchCamera: async () => {
    try {
      const sipService = await getSipService();
      await sipService.switchCamera();
    } catch (error) {
      console.error('Error switching camera:', error);
    }
  },

  // Media stream actions
  updateSession: (session: Session | null) => {
    set({ session });
  },

  updateLocalStream: (localStream: MediaStream | null) => {
    set({ localStream });
  },

  updateRemoteStream: (remoteStream: MediaStream | null) => {
    set({
      remoteStream,
      remoteStreamAvailable: !!remoteStream
    });
  },

  addTrackToRemoteStream: (track: RNMediaStreamTrack) => {
    const { remoteStream } = get();

    if (!remoteStream) {
      // Create a new remote stream if it doesn't exist
      const newRemoteStream = new MediaStream();
      newRemoteStream.addTrack(track);

      set({
        remoteStream: newRemoteStream,
        remoteStreamAvailable: true,
        // If this is a video track, update video availability
        remoteVideoAvailable: track.kind === 'video' ? true : get().remoteVideoAvailable
      });

      console.log(`Created new remote stream and added ${track.kind} track`);
    } else {
      // Add the track to the existing remote stream
      // First check if the track is already in the stream to avoid duplicates
      const existingTrack = remoteStream.getTracks().find(t => t.id === track.id);
      if (!existingTrack) {
        remoteStream.addTrack(track);

        set({
          // If this is a video track, update video availability
          remoteVideoAvailable: track.kind === 'video' ? true : get().remoteVideoAvailable
        });

        console.log(`Added ${track.kind} track to existing remote stream`);
      }
    }
  },

  // Storage functions
  saveSettings: async () => {
    try {
      const { formData } = get();
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  },

  // Registration methods and state management

  loadSettings: async () => {
    try {
      // Load saved settings
      const savedSettings = await AsyncStorage.getItem(STORAGE_KEY);

      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings) as RegistrationFormData;
        set({ formData: parsedSettings });

        // Auto-register will be handled in the SipProvider component
      }

      // Load last called destination
      try {
        const lastDestination = await AsyncStorage.getItem(LAST_DESTINATION_KEY);
        if (lastDestination) {
          set({ lastCalledDestination: lastDestination });
          console.log(`Loaded last called destination: ${lastDestination}`);
        }
      } catch (destinationError) {
        console.error('Error loading last called destination:', destinationError);
      }

      return true;
    } catch (error) {
      console.error('Error loading settings:', error);
      return false;
    }
  },

  // Computed properties
  get hasOngoingCall() {
    return get().callStatus !== 'idle';
  },

  get sipUri() {
    return get().formData.sipUri;
  },

  get displayName() {
    return get().formData.displayName;
  },
}));
