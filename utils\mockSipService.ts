import { RegistrationFormData } from '@/types/sip';
import type { SipState } from '@/store/sipStore';

/**
 * Simple interface for SIP service
 */
export interface ISipService {
  setStateUpdateCallback: (callback: (state: Partial<SipState>) => void) => void;
  register: (data: RegistrationFormData) => Promise<boolean>;
  unregister: () => Promise<boolean>;
  makeCall: (destination: string, enableVideo: boolean) => Promise<void>;
  hangupCall: () => Promise<void>;
  answerCall: () => Promise<void>;
  setMicrophoneMuted: (muted: boolean) => Promise<void>;
  setVideoMuted: (muted: boolean) => Promise<void>;
  switchCamera: () => Promise<void>;
  initialize: () => Promise<boolean>;
  destroy: () => Promise<void>;
}

/**
 * Simple mock SIP service
 */
class MockSipService implements ISipService {
  private callback: ((state: Partial<SipState>) => void) | null = null;
  private isRegistered = false;
  private callActive = false;

  // Set callback for state updates
  setStateUpdateCallback(callback: (state: Partial<SipState>) => void): void {
    this.callback = callback;
  }

  // Initialize service
  async initialize(): Promise<boolean> {
    return true;
  }

  // Clean up
  async destroy(): Promise<void> {
    this.isRegistered = false;
    this.callActive = false;
    this.updateState({
      registrationStatus: 'not_registered',
      callStatus: 'idle'
    });
  }

  // Register with SIP server
  async register(data: RegistrationFormData): Promise<boolean> {
    this.updateState({ registrationStatus: 'registering' });

    // Simulate delay
    await this.delay(1000);

    this.isRegistered = true;
    this.updateState({
      registrationStatus: 'registered',
      sipUri: data.sipUri
    });

    return true;
  }

  // Unregister from SIP server
  async unregister(): Promise<boolean> {
    this.isRegistered = false;
    this.updateState({ registrationStatus: 'not_registered' });
    return true;
  }

  // Make a call
  async makeCall(destination: string): Promise<void> {
    if (!this.isRegistered) return;

    this.updateState({
      callStatus: 'calling',
      callInfo: {
        destination,
        direction: 'outgoing',
        startTime: new Date()
      }
    });

    // Simulate connection delay
    await this.delay(2000);

    this.callActive = true;
    this.updateState({ callStatus: 'active' });
  }

  // Hang up a call
  async hangupCall(): Promise<void> {
    this.callActive = false;
    this.updateState({
      callStatus: 'idle',
      callInfo: undefined
    });
  }

  // Answer an incoming call
  async answerCall(): Promise<void> {
    this.callActive = true;
    this.updateState({ callStatus: 'active' });
  }

  // Mute/unmute microphone
  async setMicrophoneMuted(muted: boolean): Promise<void> {
    this.updateState({ isMicrophoneMuted: muted });
  }

  // Mute/unmute video
  async setVideoMuted(muted: boolean): Promise<void> {
    this.updateState({ isVideoMuted: muted });
  }

  // Switch camera
  async switchCamera(): Promise<void> {
    // No-op in mock implementation
  }

  // Helper method to update state
  private updateState(state: Partial<SipState>): void {
    if (this.callback) {
      this.callback(state);
    }
  }

  // Helper method for delays
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Helper method to check if a call is active
  private isCallActive(): boolean {
    return this.callActive;
  }
}

// Export singleton instance
export const mockSipService = new MockSipService();
