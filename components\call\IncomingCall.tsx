import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Phone, PhoneOff } from 'lucide-react-native';
import { useSipStore } from '@/store/sipStore';
import { useTheme } from '@/context/ThemeContext';
import ControlButton from './ControlButton';

// Get screen dimensions for responsive sizing
const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function IncomingCall() {
  const { colors } = useTheme();
  const {
    answerCall,
    hangUp,
    callInfo,
  } = useSipStore();

  // <PERSON>le answering the call
  const handleAnswerCall = () => {
    answerCall();
  };

  // <PERSON>le rejecting the call
  const handleRejectCall = () => {
    hangUp();
  };

  // Get caller display name from SIP URI
  const getCallerDisplayName = () => {
    if (!callInfo?.destination) return 'Unknown Caller';
    const destination = callInfo.destination;
    let displayName = destination;
    
    try {
      if (destination.startsWith('sip:')) {
        displayName = destination.substring(4);
      }
      
      const atIndex = displayName.indexOf('@');
      if (atIndex > 0) {
        displayName = displayName.substring(0, atIndex);
      }
      
      return displayName;
    } catch {
      return destination;
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View 
        style={[
          styles.incomingCallContainer,
          { backgroundColor: colors.card }
        ]}
      >
        <View style={styles.callerInfoContainer}>
          <Text style={[styles.incomingCallText, { color: colors.primary }]}>
            Incoming Call
          </Text>
          <Text style={[styles.callerName, { color: colors.text }]}>
            {getCallerDisplayName()}
          </Text>
        </View>

        <View style={styles.callControlsContainer}>
          {/* Reject Button */}
          <ControlButton
            isActive={true}
            onPress={handleRejectCall}
            activeIcon={<PhoneOff size={24} color="white" strokeWidth={2} />}
            inactiveIcon={<PhoneOff size={24} color="white" strokeWidth={2} />}
            size={60}
            activeColor="#FF3B30"
            inactiveColor="#FF3B30"
          />

          {/* Answer Button */}
          <ControlButton
            isActive={true}
            onPress={handleAnswerCall}
            activeIcon={<Phone size={24} color="white" strokeWidth={2} />}
            inactiveIcon={<Phone size={24} color="white" strokeWidth={2} />}
            size={60}
            activeColor="#4CD964"
            inactiveColor="#4CD964"
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  incomingCallContainer: {
    width: SCREEN_WIDTH - 32,
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  callerInfoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  incomingCallText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginBottom: 8,
  },
  callerName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
  },
  callControlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 16,
  },
});
