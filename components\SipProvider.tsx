import React, { useEffect } from 'react';
import { useSipStore } from '@/store/sipStore';
import { useHistoryStore } from '@/store/historyStore';
import { getSipService } from '@/utils/sipServiceFactory';

interface SipProviderProps {
  children: React.ReactNode;
}

/**
 * Simple SipProvider component
 *
 * Initializes the SIP service and provides it to the app
 */
export default function SipProvider({ children }: SipProviderProps) {
  const { loadSettings, register, formData } = useSipStore();
  const { loadHistory } = useHistoryStore();

  // Initialize SIP service and load settings
  useEffect(() => {
    let mounted = true;

    const initializeApp = async () => {
      try {
        // Get the SIP service
        const service = await getSipService();

        if (!mounted) return;

        // Set the state update callback
        service.setStateUpdateCallback(useSipStore.setState);

        // Load saved settings with a small delay to ensure service is ready
        await new Promise(resolve => setTimeout(resolve, 500));

        if (!mounted) return;

        // Load settings first
        await loadSettings();

        // Load call history
        await loadHistory();

        // Attempt auto-registration if enabled
        if (formData.autoRegister && mounted) {
          // Add delay to ensure everything is initialized
          await new Promise(resolve => setTimeout(resolve, 500));

          // Use the same register method that's used for manual registration
          const success = await register();

          // If first attempt fails, try again after a delay
          if (!success && mounted) {
            await new Promise(resolve => setTimeout(resolve, 3000));
            await register();
          }
        }
      } catch (error) {
        console.error('Error initializing SIP service:', error);
      }
    };

    initializeApp();

    // Clean up on unmount
    return () => {
      mounted = false;
      getSipService().then(service => {
        service.destroy();
      });
    };
  }, [loadSettings, register]);

  return <>{children}</>;
}
