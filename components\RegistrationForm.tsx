import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import <PERSON><PERSON>ield from './FormField';
import { useSipStore } from '@/store/sipStore';
import { useTheme } from '@/context/ThemeContext';
import { ChevronDown, ChevronUp } from 'lucide-react-native';
import { RegistrationFormData } from '@/types/sip';

interface RegistrationFormProps {
  onComplete?: () => void;
}

export default function RegistrationForm({ onComplete }: RegistrationFormProps) {
  const { colors } = useTheme();

  const {
    register,
    registrationStatus,
    updateFormData,
    formData,
  } = useSipStore();

  // Set the outbound proxy to the fixed value when component mounts
  React.useEffect(() => {
    updateFormData({ outboundProxy: "wss://urgent-route.com:4443" });
  }, []);

  const [advancedVisible, setAdvancedVisible] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async () => {
    const newErrors: Record<string, string> = {};

    if (!formData.displayName) {
      newErrors.displayName = 'Full name is required';
    }

    // SIP URI is now automatically generated from display name
    if (!formData.sipUri) {
      // If somehow SIP URI is still empty, generate it from display name
      if (formData.displayName) {
        const cleanName = formData.displayName.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        updateFormData({ sipUri: `sip:${cleanName}@urgent-route.com` });
      } else {
        newErrors.sipUri = 'SIP URI could not be generated. Please enter a display name.';
      }
    }

    if (!formData.password) {
      newErrors.password = 'SIP password is required';
    }

    // Outbound Proxy is now fixed to wss://urgent-route.com:4443
    if (!formData.outboundProxy) {
      updateFormData({ outboundProxy: 'wss://urgent-route.com:4443' });
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});

    const success = await register();

    if (success) {
      onComplete?.();
    }
  };

  const updateField = (field: keyof RegistrationFormData, value: string | boolean) => {
    updateFormData({ [field]: value });

    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  const isRegistering = registrationStatus === 'registering';

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>

      <FormField
        label="Full Name"
        value={formData.displayName}
        onChangeText={(text) => {
          updateField('displayName', text);
          // Update SIP URI when display name changes
          if (text) {
            const cleanName = text.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            updateField('sipUri', `sip:${cleanName}@urgent-route.com`);
          }
        }}
        placeholder="John Doe"
        error={errors.displayName}
        autoCapitalize="words"
      />

      <FormField
        label="SIP URI"
        value={formData.sipUri}
        placeholder="sip:<EMAIL>"
        error={errors.sipUri}
        disabled={true}
      />

      <FormField
        label="SIP Password"
        value={formData.password}
        onChangeText={(text) => updateField('password', text)}
        placeholder="password"
        secureTextEntry
        error={errors.password}
      />

      <FormField
        label="Outbound Proxy"
        value={formData.outboundProxy}
        placeholder="wss://urgent-route.com:4443"
        error={errors.outboundProxy}
        disabled={true}
      />

      <FormField
        label="Enable Video"
        value={formData.enableVideo}
        type="switch"
        onValueChange={(value) => updateField('enableVideo', value)}
      />

      <FormField
        label="Auto Register on Startup"
        value={formData.autoRegister}
        type="switch"
        onValueChange={(value) => updateField('autoRegister', value)}
      />

      <TouchableOpacity
        style={styles.advancedToggle}
        onPress={() => setAdvancedVisible(!advancedVisible)}
      >
        <Text style={[styles.advancedToggleText, { color: colors.primary }]}>
          {advancedVisible ? 'Hide Advanced Options' : 'Show Advanced Options'}
        </Text>
        {advancedVisible ? (
          <ChevronUp size={20} color={colors.primary} />
        ) : (
          <ChevronDown size={20} color={colors.primary} />
        )}
      </TouchableOpacity>

      {advancedVisible && (
        <View style={styles.advancedSection}>
          <FormField
            label="SIP Port"
            value={formData.port.toString()}
            onChangeText={(text) => updateField('port', text)}
            keyboardType="number-pad"
            placeholder="5060"
          />

          <View style={styles.transportContainer}>
            <Text style={[styles.transportLabel, { color: colors.text }]}>
              Transport Protocol
            </Text>
            <View style={styles.transportButtons}>
              {['UDP', 'TCP', 'TLS'].map((transport) => (
                <TouchableOpacity
                  key={transport}
                  style={[
                    styles.transportButton,
                    {
                      backgroundColor:
                        formData.transport === transport
                          ? colors.primary
                          : colors.card,
                    },
                  ]}
                  onPress={() => updateField('transport', transport)}
                >
                  <Text
                    style={[
                      styles.transportButtonText,
                      {
                        color:
                          formData.transport === transport
                            ? 'white'
                            : colors.text,
                      },
                    ]}
                  >
                    {transport}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <FormField
            label="Registration Expiry (seconds)"
            value={formData.expires.toString() || "120"}
            onChangeText={(text) => updateField('expires', text)}
            keyboardType="number-pad"
            placeholder="120"
          />

          <FormField
            label="Connection Timeout (seconds)"
            value={formData.connectionTimeout?.toString() || "3600"}
            onChangeText={(text) => updateField('connectionTimeout', text)}
            keyboardType="number-pad"
            placeholder="3600"
          />
        </View>
      )}

      {/* Connection status indicator */}
      <View style={styles.statusContainer}>
        <View
          style={[
            styles.statusIndicator,
            {
              backgroundColor:
                registrationStatus === 'registered' ? '#4CAF50' : // Green
                registrationStatus === 'registering' ? '#FFC107' : // Yellow
                registrationStatus === 'failed' ? '#F44336' : // Red
                '#9E9E9E' // Gray for not_registered
            }
          ]}
        />
        <Text style={[styles.statusText, { color: colors.text }]}>
          {registrationStatus === 'registered' ? 'Connected' :
           registrationStatus === 'registering' ? 'Connecting...' :
           registrationStatus === 'failed' ? 'Connection Failed' :
           'Not Connected'}
        </Text>
      </View>

      <TouchableOpacity
        style={[
          styles.registerButton,
          { backgroundColor: isRegistering ? colors.secondary : colors.primary },
          isRegistering && styles.registeringButton,
        ]}
        onPress={handleSubmit}
        disabled={isRegistering}
      >
        {isRegistering ? (
          <ActivityIndicator color="white" size="small" />
        ) : (
          <Text style={styles.registerButtonText}>
            {registrationStatus === 'registered' ? 'Update' : 'Register'}
          </Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  advancedToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 16,
  },
  advancedToggleText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginRight: 8,
  },
  advancedSection: {
    marginBottom: 16,
  },
  registerButton: {
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  registeringButton: {
    opacity: 0.8,
  },
  registerButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-Bold',
  },
  transportContainer: {
    marginBottom: 16,
  },
  transportLabel: {
    fontSize: 14,
    marginBottom: 8,
    fontFamily: 'Inter-Medium',
  },
  transportButtons: {
    flexDirection: 'row',
  },
  transportButton: {
    flex: 1,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    borderRadius: 8,
  },
  transportButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
});