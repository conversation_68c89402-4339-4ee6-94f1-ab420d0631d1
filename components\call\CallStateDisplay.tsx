import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import CallingAnimation from './CallingAnimation';
import AvatarPlaceholder from './AvatarPlaceholder';

// Define detailed call states
export type DetailedCallState = 
  | 'initializing'
  | 'gathering_ice'
  | 'ice_complete'
  | 'connecting'
  | 'connected'
  | 'reconnecting'
  | 'checking'
  | 'failed'
  | 'disconnected'
  | 'closed';

interface CallStateDisplayProps {
  callStatus: string;
  detailedCallState: DetailedCallState;
  callDuration: number;
  displayName: string;
}

export default function CallStateDisplay({
  callStatus,
  detailedCallState,
  callDuration,
  displayName
}: CallStateDisplayProps) {
  const { colors } = useTheme();

  // Format duration as MM:SS
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get the appropriate text for the detailed call state
  const getDetailedCallStateText = (): string => {
    switch (detailedCallState) {
      case 'initializing':
        return 'Initializing call...';
      case 'gathering_ice':
        return 'Gathering network routes...';
      case 'ice_complete':
        return 'Network routes gathered';
      case 'connecting':
        return 'Connecting to peer...';
      case 'checking':
        return 'Checking connection...';
      case 'connected':
        return 'Connected';
      case 'reconnecting':
        return 'Reconnecting...';
      case 'failed':
        return 'Connection failed';
      case 'disconnected':
        return 'Disconnected';
      case 'closed':
        return 'Call ended';
      default:
        return 'Calling...';
    }
  };

  return (
    <View style={[styles.noVideoPlaceholder, { backgroundColor: colors.card }]}>
      {callStatus === 'active' ? (
        <AvatarPlaceholder name={displayName} />
      ) : (
        <CallingAnimation />
      )}
      <Text style={[styles.callerName, { color: colors.text }]}>
        {displayName}
      </Text>
      
      {/* Detailed call state or duration */}
      <Text style={[styles.detailedState, { color: colors.textSecondary }]}>
        {callStatus === 'active' 
          ? formatDuration(callDuration) 
          : getDetailedCallStateText()}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  noVideoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
  },
  callerName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
    marginTop: 20,
    textAlign: 'center',
  },
  detailedState: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    textAlign: 'center',
    marginTop: 10,
  },
});
