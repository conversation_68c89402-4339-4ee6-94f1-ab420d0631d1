import { useNotification } from '@/context/NotificationContext';

// This is a singleton that holds the notification function
// It's used to show notifications from stores that don't have access to hooks
let showNotificationFn: ReturnType<typeof useNotification>['showNotification'] | null = null;

export const setNotificationFunction = (
  fn: ReturnType<typeof useNotification>['showNotification']
) => {
  showNotificationFn = fn;
};

export const showNotification = (
  type: 'error' | 'success' | 'info' | 'warning',
  message: string,
  title?: string,
  autoClose = true,
  duration = 5000
) => {
  if (showNotificationFn) {
    return showNotificationFn(type, message, title, autoClose, duration);
  } else {
    console.warn('Notification function not set. Call setNotificationFunction first.');
    return '';
  }
};
