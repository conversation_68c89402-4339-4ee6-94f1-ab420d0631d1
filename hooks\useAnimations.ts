import { useRef } from 'react';
import { Animated, Easing } from 'react-native';

interface AnimationOptions {
  duration?: number;
  useNativeDriver?: boolean;
  easing?: Animated.EasingFunction;
}

interface SpringOptions {
  friction?: number;
  tension?: number;
  useNativeDriver?: boolean;
}

export function useAnimations() {
  // Create a new animated value
  const createValue = (initialValue: number) => {
    return useRef(new Animated.Value(initialValue)).current;
  };

  // Timing animation
  const timing = (
    value: Animated.Value,
    toValue: number,
    options: AnimationOptions = {}
  ) => {
    const { duration = 300, useNativeDriver = true, easing = Easing.ease } = options;
    
    return Animated.timing(value, {
      toValue,
      duration,
      easing,
      useNativeDriver,
    });
  };

  // Spring animation
  const spring = (
    value: Animated.Value,
    toValue: number,
    options: SpringOptions = {}
  ) => {
    const { friction = 7, tension = 40, useNativeDriver = true } = options;
    
    return Animated.spring(value, {
      toValue,
      friction,
      tension,
      useNativeDriver,
    });
  };

  // Sequence of animations
  const sequence = (animations: Animated.CompositeAnimation[]) => {
    return Animated.sequence(animations);
  };

  // Parallel animations
  const parallel = (animations: Animated.CompositeAnimation[]) => {
    return Animated.parallel(animations);
  };

  // Button press animation
  const buttonPress = (scaleValue: Animated.Value) => {
    return sequence([
      timing(scaleValue, 0.95, { duration: 100 }),
      timing(scaleValue, 1, { duration: 100 }),
    ]);
  };

  // Slide in animation
  const slideIn = (
    value: Animated.Value,
    from: 'left' | 'right' | 'top' | 'bottom',
    distance: number,
    options: SpringOptions = {}
  ) => {
    // Set initial value based on direction
    let initialValue = 0;
    switch (from) {
      case 'left':
        initialValue = -distance;
        break;
      case 'right':
        initialValue = distance;
        break;
      case 'top':
        initialValue = -distance;
        break;
      case 'bottom':
        initialValue = distance;
        break;
    }
    
    value.setValue(initialValue);
    return spring(value, 0, options);
  };

  // Slide out animation
  const slideOut = (
    value: Animated.Value,
    to: 'left' | 'right' | 'top' | 'bottom',
    distance: number,
    options: SpringOptions = {}
  ) => {
    // Set target value based on direction
    let targetValue = 0;
    switch (to) {
      case 'left':
        targetValue = -distance;
        break;
      case 'right':
        targetValue = distance;
        break;
      case 'top':
        targetValue = -distance;
        break;
      case 'bottom':
        targetValue = distance;
        break;
    }
    
    return spring(value, targetValue, options);
  };

  // Fade in animation
  const fadeIn = (value: Animated.Value, options: AnimationOptions = {}) => {
    value.setValue(0);
    return timing(value, 1, options);
  };

  // Fade out animation
  const fadeOut = (value: Animated.Value, options: AnimationOptions = {}) => {
    return timing(value, 0, options);
  };

  return {
    createValue,
    timing,
    spring,
    sequence,
    parallel,
    buttonPress,
    slideIn,
    slideOut,
    fadeIn,
    fadeOut,
  };
}
