{"expo": {"name": "sip-tester", "slug": "sip-tester", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bitcode": false, "bundleIdentifier": "com.tkabe.siptester", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["@config-plugins/react-native-webrtc"], "experiments": {"typedRoutes": true}, "android": {"package": "com.tkabe.siptester", "permissions": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CAMERA", "android.permission.INTERNET", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.RECORD_AUDIO", "android.permission.SYSTEM_ALERT_WINDOW", "android.permission.WAKE_LOCK", "android.permission.BLUETOOTH"]}, "extra": {"router": {"origin": false}, "eas": {"projectId": "b6ff1839-74a1-47cc-a051-d03da8649308"}}}}