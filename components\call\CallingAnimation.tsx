import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated, Easing, Dimensions } from 'react-native';
import { Phone } from 'lucide-react-native';

// Get screen dimensions for responsive sizing
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function CallingAnimation() {
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const secondPulseAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Create continuous animations with proper looping
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.6,
          duration: 1200,
          useNativeDriver: true,
          easing: Easing.out(Easing.ease),
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: true,
          easing: Easing.in(Easing.ease),
        }),
      ]),
      { iterations: -1 } // Ensure infinite looping
    );

    const secondPulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(secondPulseAnim, {
          toValue: 1.8,
          duration: 1200,
          useNativeDriver: true,
          easing: Easing.out(Easing.ease),
        }),
        Animated.timing(secondPulseAnim, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: true,
          easing: Easing.in(Easing.ease),
        }),
      ]),
      { iterations: -1 } // Ensure infinite looping
    );

    // Improved rotation animation using sine wave pattern for perfect looping
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2400, // Full cycle duration
        useNativeDriver: true,
        easing: Easing.inOut(Easing.sin), // Sine wave pattern ensures smooth transitions at loop points
      }),
      { iterations: -1 } // Ensure infinite looping
    );

    const opacityAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(opacityAnim, {
          toValue: 0.4,
          duration: 1200,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
        Animated.timing(opacityAnim, {
          toValue: 0.8,
          duration: 1200,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
      ]),
      { iterations: -1 } // Ensure infinite looping
    );

    // Start animations
    pulseAnimation.start();
    secondPulseAnimation.start();
    rotateAnimation.start();
    opacityAnimation.start();

    // Clean up animations on unmount
    return () => {
      pulseAnimation.stop();
      secondPulseAnimation.stop();
      rotateAnimation.stop();
      opacityAnimation.stop();
    };
  }, []);

  // Improved rotation interpolation with perfect looping
  // Using more interpolation points for smoother transitions and ensuring start/end values match
  const rotate = rotateAnim.interpolate({
    inputRange: [0, 0.125, 0.25, 0.375, 0.5, 0.625, 0.75, 0.875, 1],
    outputRange: ['0deg', '-10deg', '5deg', '-8deg', '10deg', '-8deg', '5deg', '-10deg', '0deg'],
    // Start and end values match (0deg) for seamless looping
    // The pattern creates a natural-looking "wiggle" that smoothly transitions back to start
  });

  // Make size responsive based on screen dimensions - larger now
  const containerSize = Math.min(SCREEN_WIDTH, SCREEN_HEIGHT) * 0.5;
  const circleSize = containerSize * 0.7;
  const secondCircleSize = containerSize;
  const iconContainerSize = containerSize * 0.7;

  return (
    <View style={[styles.callingAnimationContainer, { width: containerSize, height: containerSize }]}>
      <Animated.View
        style={[
          styles.pulseCircle,
          {
            width: secondCircleSize,
            height: secondCircleSize,
            borderRadius: secondCircleSize / 2,
            opacity: Animated.multiply(opacityAnim, 0.3),
            transform: [{ scale: secondPulseAnim }],
          },
        ]}
      />
      <Animated.View
        style={[
          styles.pulseCircle,
          {
            width: circleSize,
            height: circleSize,
            borderRadius: circleSize / 2,
            backgroundColor: 'rgba(0, 150, 136, 0.3)',
            opacity: opacityAnim,
            transform: [{ scale: pulseAnim }],
          },
        ]}
      />
      <Animated.View
        style={{
          transform: [{ scale: 1 }, { rotate }],
        }}
      >
        <View style={[styles.phoneIconContainer, { width: iconContainerSize, height: iconContainerSize, borderRadius: iconContainerSize / 2 }]}>
          <Phone size={iconContainerSize * 0.6} color="white" />
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  callingAnimationContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginBottom: 24,
  },
  pulseCircle: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  phoneIconContainer: {
    backgroundColor: 'rgba(0, 150, 136, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 10,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
});
